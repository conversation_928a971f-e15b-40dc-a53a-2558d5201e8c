"""
系统测试脚本
验证多标签页AI模型查询系统的各个组件是否正常工作
"""

import asyncio
import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from core.task_scheduler import TaskScheduler
from core.browser_manager import BrowserManager
from core.result_processor import ResultProcessor
from utils.logger import app_logger

class SystemTester:
    def __init__(self):
        self.logger = app_logger
        self.test_results = []

    def log_test(self, test_name: str, success: bool, message: str = ""):
        """记录测试结果"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")
        self.test_results.append({
            'test': test_name,
            'success': success,
            'message': message
        })

    def test_config_loading(self):
        """测试配置文件加载"""
        try:
            scheduler = TaskScheduler()
            models = scheduler.get_available_models()

            if models:
                self.log_test("配置文件加载", True, f"加载了 {len(models)} 个模型")
                return True
            else:
                self.log_test("配置文件加载", False, "没有找到任何模型配置")
                return False
        except Exception as e:
            self.log_test("配置文件加载", False, str(e))
            return False

    def test_browser_manager(self):
        """测试浏览器管理器"""
        try:
            browser = BrowserManager(headless=False)  # 使用无头模式测试

            # 测试浏览器启动
            if browser.start_browser():
                self.log_test("浏览器启动", True, "浏览器启动成功")

                # 测试打开标签页
                tab_handle = browser.open_tab("https://www.baidu.com", "测试页面")
                if tab_handle:
                    self.log_test("标签页打开", True, "成功打开测试标签页")

                    # 测试切换标签页
                    if browser.switch_to_tab(tab_handle):
                        self.log_test("标签页切换", True, "成功切换到测试标签页")
                    else:
                        self.log_test("标签页切换", False, "切换标签页失败")

                    # 清理
                    browser.close_tab(tab_handle)
                else:
                    self.log_test("标签页打开", False, "打开标签页失败")

                browser.quit()
                return True
            else:
                self.log_test("浏览器启动", False, "浏览器启动失败")
                return False

        except Exception as e:
            self.log_test("浏览器管理器", False, str(e))
            return False

    def test_result_processor(self):
        """测试结果处理器"""
        try:
            processor = ResultProcessor()

            # 创建测试数据
            test_results = [
                {
                    'model_name': '测试模型1',
                    'question': '测试问题',
                    'response': '这是测试响应内容',
                    'success': True,
                    'error': None,
                    'timestamp': 1234567890
                },
                {
                    'model_name': '测试模型2',
                    'question': '测试问题',
                    'response': None,
                    'success': False,
                    'error': '测试错误',
                    'timestamp': 1234567890
                }
            ]

            # 测试结果处理
            result = processor.process_results(test_results, "测试问题", "markdown")

            if result.get('success'):
                self.log_test("结果处理", True, f"成功生成 {len(result['output_files'])} 个文件")

                # 清理测试文件
                for file_path in result['output_files'].values():
                    try:
                        Path(file_path).unlink(missing_ok=True)
                    except:
                        pass

                return True
            else:
                self.log_test("结果处理", False, result.get('error', '未知错误'))
                return False

        except Exception as e:
            self.log_test("结果处理器", False, str(e))
            return False

    def test_logger(self):
        """测试日志系统"""
        try:
            # 测试各种日志级别
            self.logger.info("测试信息日志")
            self.logger.warning("测试警告日志")
            self.logger.error("测试错误日志")

            self.log_test("日志系统", True, "日志记录正常")
            return True
        except Exception as e:
            self.log_test("日志系统", False, str(e))
            return False

    def test_dependencies(self):
        """测试依赖包"""
        required_packages = [
            'selenium',
            'click',
            'loguru',
            'bs4',  # beautifulsoup4 imports as bs4
            'markdown'
        ]

        missing_packages = []

        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                missing_packages.append(package)

        if not missing_packages:
            self.log_test("依赖检查", True, f"所有 {len(required_packages)} 个依赖包都已安装")
            return True
        else:
            self.log_test("依赖检查", False, f"缺少依赖包: {', '.join(missing_packages)}")
            return False

    async def run_all_tests(self):
        """运行所有测试"""
        print("🧪 系统测试开始")
        print("=" * 50)

        # 运行各项测试
        tests = [
            ("依赖检查", self.test_dependencies),
            ("日志系统", self.test_logger),
            ("配置文件加载", self.test_config_loading),
            ("结果处理器", self.test_result_processor),
            # ("浏览器管理器", self.test_browser_manager),  # 可能需要较长时间，可选
        ]

        passed = 0
        total = len(tests)

        for test_name, test_func in tests:
            print(f"\n🔍 运行测试: {test_name}")
            try:
                if test_func():
                    passed += 1
            except Exception as e:
                self.log_test(test_name, False, f"测试异常: {e}")

        # 输出测试总结
        print("\n" + "=" * 50)
        print(f"📊 测试总结: {passed}/{total} 通过")

        if passed == total:
            print("🎉 所有测试通过! 系统运行正常")
        else:
            print("⚠️  部分测试失败，请检查相关组件")

            # 显示失败的测试
            failed_tests = [r for r in self.test_results if not r['success']]
            if failed_tests:
                print("\n❌ 失败的测试:")
                for test in failed_tests:
                    print(f"  • {test['test']}: {test['message']}")

        return passed == total

async def main():
    """主函数"""
    tester = SystemTester()
    success = await tester.run_all_tests()

    if not success:
        print("\n💡 建议:")
        print("1. 检查 requirements.txt 中的依赖是否都已安装")
        print("2. 确认配置文件 config/models.json 存在且格式正确")
        print("3. 检查系统是否安装了Chrome浏览器")
        print("4. 查看日志文件获取详细错误信息")

if __name__ == "__main__":
    asyncio.run(main())
