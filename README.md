# 多标签页AI模型查询系统

一个基于Python的工具，可以同时在多个AI模型网站上查询问题，并汇总结果。该系统利用默认浏览器复用用户账号cookie，支持并发查询多个AI模型。

## 功能特性

- 🚀 **并发查询**: 同时向多个AI模型发送问题
- 🍪 **Cookie复用**: 利用默认浏览器的登录状态
- 📊 **结果汇总**: 自动汇总和分析所有模型的响应
- 🔧 **配置化**: 支持自定义AI模型配置
- 📝 **多格式输出**: 支持Markdown、JSON、HTML格式报告
- 🎯 **交互式界面**: 提供友好的命令行交互界面

## 支持的AI模型

- 腾讯元宝 (yuanbao.tencent.com)
- 豆包 (doubao.com)
- 文心一言 (yiyan.baidu.com)
- 通义千问 (tongyi.aliyun.com)
- 智谱清言 (chatglm.cn)

## 快速开始

### 方法一：一键启动（推荐）
```bash
# Windows用户
start.bat

# 或者手动运行设置脚本
python setup.py
```

### 方法二：手动安装
```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 运行系统测试
python test_system.py

# 3. 查看功能演示
python demo.py
```

### 浏览器要求
- 确保系统中安装了Chrome浏览器
- 系统会自动检测并使用默认浏览器路径
- 无需手动下载ChromeDriver

## 使用方法

### 交互式模式（推荐）
```bash
python main_new.py
```

### 命令行模式
```bash
# 查询所有模型
python main_new.py query -q "新能源汽车的发展趋势"

# 查询指定模型
python main_new.py query -q "AI的未来发展" -m "腾讯元宝,豆包"

# 指定输出格式
python main_new.py query -q "区块链技术应用" -o json

# 查看可用模型
python main_new.py models
```

## 配置文件

配置文件位于 `config/models.json`，可以自定义AI模型的配置：

```json
{
  "ai_models": [
    {
      "name": "腾讯元宝",
      "url": "https://yuanbao.tencent.com",
      "login_required": true,
      "input_selector": "textarea[placeholder*='输入']",
      "submit_selector": "button[type='submit']",
      "output_selector": ".message-content",
      "wait_time": 3,
      "description": "腾讯元宝AI助手"
    }
  ],
  "settings": {
    "max_concurrent_tabs": 5,
    "default_wait_time": 10,
    "browser_headless": false
  }
}
```

## 输出文件

系统会在 `output/` 目录下生成以下文件：
- `report_YYYYMMDD_HHMMSS.md` - Markdown格式报告
- `report_YYYYMMDD_HHMMSS.json` - JSON格式报告
- `report_YYYYMMDD_HHMMSS.html` - HTML格式报告
- `raw_responses_YYYYMMDD_HHMMSS.json` - 原始响应数据

## 项目结构

```
mul_chrome_tabs/
├── main_new.py              # 主程序入口
├── requirements.txt         # 依赖列表
├── config/
│   └── models.json         # AI模型配置
├── core/
│   ├── browser_manager.py  # 浏览器管理
│   ├── model_handler.py    # 模型交互处理
│   ├── result_processor.py # 结果处理
│   └── task_scheduler.py   # 任务调度
├── utils/
│   └── logger.py          # 日志工具
├── tools/
│   └── utils.py           # 工具函数
├── output/                # 输出目录
└── logs/                  # 日志目录
```

## 注意事项

1. **首次使用**: 需要手动登录各个AI模型网站，系统会复用这些登录状态
2. **网络环境**: 确保网络连接稳定，能够访问目标AI模型网站
3. **浏览器版本**: 确保Chrome浏览器版本与ChromeDriver版本匹配
4. **反爬虫**: 某些网站可能有反爬虫机制，建议适当调整等待时间

## 故障排除

### 常见问题

1. **浏览器启动失败**
   - 检查Chrome浏览器是否正确安装
   - 确认ChromeDriver版本与Chrome版本匹配

2. **登录失败**
   - 手动在浏览器中登录对应网站
   - 检查网站是否更新了登录流程

3. **元素定位失败**
   - 网站可能更新了页面结构
   - 需要更新配置文件中的选择器

## 开发说明

### 添加新的AI模型

1. 在 `config/models.json` 中添加模型配置
2. 根据网站特点调整选择器和等待时间
3. 测试登录和交互流程

### 自定义结果处理

可以修改 `core/result_processor.py` 中的处理逻辑，实现自定义的结果分析和汇总功能。

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。
