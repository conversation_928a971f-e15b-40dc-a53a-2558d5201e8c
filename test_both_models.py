"""
双模型兼容性测试脚本
测试腾讯元宝和豆包两个模型的完整兼容性
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from core.task_scheduler import TaskScheduler
from utils.logger import app_logger

async def test_both_models():
    """测试腾讯元宝和豆包两个模型"""
    print("🤖 双模型兼容性测试")
    print("=" * 60)
    
    try:
        # 创建任务调度器
        scheduler = TaskScheduler()
        
        # 测试问题
        test_question = "请用一句话介绍人工智能的定义"
        
        print(f"📝 测试问题: {test_question}")
        print("🚀 开始测试腾讯元宝和豆包...")
        
        # 测试两个模型
        selected_models = ["腾讯元宝", "豆包"]
        
        # 执行查询
        result = await scheduler.run_query(test_question, selected_models)
        
        # 显示结果
        print("\n" + "=" * 60)
        print("📊 双模型测试结果:")
        
        if result.get('success'):
            print("🎉 双模型测试成功!")
            
            analysis = result.get('analysis', {})
            print(f"📈 成功响应: {analysis.get('successful_models', 0)}/{analysis.get('total_models', 0)}")
            print(f"📊 成功率: {analysis.get('success_rate', 0):.1%}")
            
            # 显示各个模型的响应
            responses = result.get('summary', {}).get('responses', [])
            
            print(f"\n📄 各模型响应详情:")
            print("-" * 40)
            
            for response in responses:
                model_name = response.get('model_name', '未知模型')
                print(f"\n🤖 {model_name}:")
                
                if response.get('success'):
                    response_text = response.get('response', '无响应')
                    print(f"  ✅ 状态: 成功")
                    print(f"  📝 回复: {response_text}")
                    print(f"  📊 长度: {len(response_text)} 字符")
                else:
                    error_msg = response.get('error', '未知错误')
                    print(f"  ❌ 状态: 失败")
                    print(f"  🔍 错误: {error_msg}")
            
            # 显示生成的文件
            output_files = result.get('output_files', {})
            if output_files:
                print(f"\n📁 生成的报告文件:")
                for file_type, file_path in output_files.items():
                    print(f"  📄 {file_type}: {file_path}")
            
            # 显示关键洞察
            insights = result.get('summary', {}).get('consolidated_insights', [])
            if insights:
                print(f"\n💡 关键洞察:")
                for insight in insights:
                    print(f"  • {insight}")
            
        else:
            print("❌ 双模型测试失败!")
            print(f"🔍 错误信息: {result.get('error', '未知错误')}")
        
        return result.get('success', False)
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现异常: {e}")
        app_logger.error(f"双模型测试异常: {e}")
        return False

async def test_configuration_summary():
    """显示配置总结"""
    print("\n🔧 当前配置总结")
    print("-" * 40)
    
    scheduler = TaskScheduler()
    
    # 获取模型配置
    models = scheduler.config.get('ai_models', [])
    
    for model in models:
        if model['name'] in ['腾讯元宝', '豆包']:
            print(f"\n🤖 {model['name']}:")
            print(f"  🌐 URL: {model['url']}")
            print(f"  📝 输入框: {model['input_selector']}")
            print(f"  🔘 发送按钮: {model['submit_selector']}")
            print(f"  📄 输出区域: {model['output_selector']}")
            print(f"  ⏱️  等待时间: {model['wait_time']}秒")

async def main():
    """主函数"""
    print("🤖 腾讯元宝 & 豆包 兼容性测试程序")
    print("=" * 70)
    
    # 显示配置总结
    await test_configuration_summary()
    
    print("\n" + "=" * 70)
    print("🚀 开始双模型测试...")
    
    success = await test_both_models()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 双模型兼容性测试完全成功!")
        print("✅ 腾讯元宝和豆包都能正常工作")
        print("✅ 所有选择器配置正确")
        print("✅ 响应获取功能正常")
        print("✅ 报告生成功能正常")
        print("\n🌟 恭喜！您的多标签页AI查询系统已完全就绪！")
        print("\n💡 现在您可以:")
        print("   1. 使用交互式模式: python main_new.py")
        print("   2. 使用命令行模式: python main_new.py query -q \"您的问题\"")
        print("   3. 查询特定模型: python main_new.py query -q \"您的问题\" -m \"腾讯元宝,豆包\"")
        print("   4. 添加更多AI模型到配置文件中")
    else:
        print("⚠️  双模型测试需要进一步调试")
        print("💡 建议:")
        print("  1. 检查网络连接")
        print("  2. 确认两个模型都已正确登录")
        print("  3. 查看详细的错误日志")
        print("  4. 尝试单独测试每个模型")

if __name__ == "__main__":
    asyncio.run(main())
