2025-06-22 12:05:08 | INFO     | utils.logger:info:61 - 测试信息日志
2025-06-22 12:05:08 | WARNING  | utils.logger:warning:69 - 测试警告日志
2025-06-22 12:05:08 | ERROR    | utils.logger:error:73 - 测试错误日志
2025-06-22 12:05:08 | INFO     | utils.logger:info:61 - 结果处理完成，生成文件: ['output\\report_20250622_120508.md', 'output\\raw_responses_20250622_120508.json']
2025-06-22 12:05:41 | INFO     | utils.logger:info:61 - 测试信息日志
2025-06-22 12:05:41 | WARNING  | utils.logger:warning:69 - 测试警告日志
2025-06-22 12:05:41 | ERROR    | utils.logger:error:73 - 测试错误日志
2025-06-22 12:05:41 | INFO     | utils.logger:info:61 - 配置文件加载成功: config\models.json
2025-06-22 12:05:41 | INFO     | utils.logger:info:61 - 结果处理完成，生成文件: ['output\\report_20250622_120541.md', 'output\\raw_responses_20250622_120541.json']
2025-06-22 12:05:48 | INFO     | utils.logger:info:61 - 配置文件加载成功: config\models.json
2025-06-22 12:06:28 | INFO     | utils.logger:info:61 - 配置文件加载成功: config\models.json
2025-06-22 12:06:28 | INFO     | utils.logger:info:61 - 配置文件加载成功: config\models.json
2025-06-22 12:06:28 | INFO     | utils.logger:info:61 - 添加模型配置成功: 演示AI
2025-06-22 12:06:28 | INFO     | utils.logger:info:61 - 移除模型配置成功: 演示AI
2025-06-22 12:09:10 | INFO     | utils.logger:info:61 - 配置文件加载成功: config\models.json
2025-06-22 12:09:10 | INFO     | utils.logger:info:61 - 设置更新成功
2025-06-22 12:09:10 | INFO     | utils.logger:info:61 - 开始执行查询任务: AI的未来发展
2025-06-22 12:09:10 | INFO     | utils.logger:info:61 - 使用浏览器路径: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-06-22 12:11:36 | INFO     | utils.logger:info:61 - 测试信息日志
2025-06-22 12:11:36 | WARNING  | utils.logger:warning:69 - 测试警告日志
2025-06-22 12:11:36 | ERROR    | utils.logger:error:73 - 测试错误日志
2025-06-22 12:11:36 | INFO     | utils.logger:info:61 - 配置文件加载成功: config\models.json
2025-06-22 12:11:36 | INFO     | utils.logger:info:61 - 结果处理完成，生成文件: ['output\\report_20250622_121136.md', 'output\\raw_responses_20250622_121136.json']
2025-06-22 12:15:19 | INFO     | utils.logger:info:61 - 使用浏览器路径: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-06-22 12:15:22 | INFO     | utils.logger:info:61 - 浏览器启动成功
2025-06-22 12:16:02 | INFO     | utils.logger:info:61 - 成功打开标签页: 豆包测试 - https://www.doubao.com
2025-06-22 12:16:49 | INFO     | utils.logger:info:61 - 浏览器已退出
2025-06-22 12:17:14 | INFO     | utils.logger:info:61 - 使用浏览器路径: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-06-22 12:17:16 | INFO     | utils.logger:info:61 - 浏览器启动成功
2025-06-22 12:17:55 | INFO     | utils.logger:info:61 - 成功打开标签页: 豆包 - https://www.doubao.com
2025-06-22 12:18:13 | WARNING  | utils.logger:warning:69 - 等待元素超时: textarea[placeholder*='请输入']
2025-06-22 12:18:32 | ERROR    | utils.logger:error:73 - 页面元素测试异常: 
2025-06-22 12:18:32 | ERROR    | utils.logger:error:73 - 切换标签页失败: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-06-22 12:19:41 | INFO     | utils.logger:info:61 - 使用浏览器路径: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-06-22 12:19:42 | INFO     | utils.logger:info:61 - 浏览器启动成功
2025-06-22 12:19:51 | INFO     | utils.logger:info:61 - 成功打开标签页: 豆包 - https://www.doubao.com
2025-06-22 12:20:06 | WARNING  | utils.logger:warning:69 - 等待元素超时: textarea[placeholder*='请输入']
2025-06-22 12:20:11 | INFO     | utils.logger:info:61 - 开始与 豆包 交互
2025-06-22 12:20:13 | INFO     | utils.logger:info:61 - 豆包 已登录
2025-06-22 12:20:15 | INFO     | utils.logger:info:61 - 成功发送输入: 你好，请简单介绍一下自己...
2025-06-22 12:20:16 | INFO     | utils.logger:info:61 - 成功点击元素: button[aria-label='发送']
2025-06-22 12:20:16 | INFO     | utils.logger:info:61 - 等待 豆包 响应...
2025-06-22 12:21:22 | INFO     | utils.logger:info:61 - 豆包 响应成功
2025-06-22 12:21:24 | INFO     | utils.logger:info:61 - 浏览器已退出
2025-06-22 12:28:08 | INFO     | utils.logger:info:61 - 使用浏览器路径: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-06-22 12:28:10 | INFO     | utils.logger:info:61 - 浏览器启动成功
2025-06-22 12:28:11 | INFO     | utils.logger:info:61 - 成功打开标签页: 豆包 - https://www.doubao.com
2025-06-22 12:28:36 | INFO     | utils.logger:info:61 - 浏览器已退出
2025-06-22 12:29:27 | INFO     | utils.logger:info:61 - 使用浏览器路径: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-06-22 12:29:29 | INFO     | utils.logger:info:61 - 浏览器启动成功
2025-06-22 12:29:31 | INFO     | utils.logger:info:61 - 成功打开标签页: 豆包分析 - https://www.doubao.com
2025-06-22 12:30:04 | INFO     | utils.logger:info:61 - 浏览器已退出
2025-06-22 12:31:58 | INFO     | utils.logger:info:61 - 配置文件加载成功: config\models.json
2025-06-22 12:31:58 | INFO     | utils.logger:info:61 - 开始执行查询任务: 你好，请简单介绍一下自己，不超过100字
2025-06-22 12:31:58 | INFO     | utils.logger:info:61 - 使用浏览器路径: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-06-22 12:31:59 | INFO     | utils.logger:info:61 - 浏览器启动成功
2025-06-22 12:31:59 | INFO     | utils.logger:info:61 - 组件初始化完成
2025-06-22 12:32:01 | INFO     | utils.logger:info:61 - 成功打开标签页: 豆包 - https://www.doubao.com
2025-06-22 12:32:01 | INFO     | utils.logger:info:61 - 成功打开 豆包 标签页
2025-06-22 12:32:03 | INFO     | utils.logger:info:61 - 开始与 豆包 交互
2025-06-22 12:32:05 | INFO     | utils.logger:info:61 - 豆包 已登录
2025-06-22 12:32:07 | INFO     | utils.logger:info:61 - 成功发送输入: 你好，请简单介绍一下自己，不超过100字...
2025-06-22 12:32:08 | INFO     | utils.logger:info:61 - 成功点击元素: button[aria-label*='发送'], button[title*='发送'], button:has(svg)
2025-06-22 12:32:08 | INFO     | utils.logger:info:61 - 等待 豆包 响应...
2025-06-22 12:32:32 | INFO     | utils.logger:info:61 - 豆包 响应成功
2025-06-22 12:32:32 | INFO     | utils.logger:info:61 - 结果处理完成，生成文件: ['output\\report_20250622_123232.md', 'output\\raw_responses_20250622_123232.json']
2025-06-22 12:32:32 | INFO     | utils.logger:info:61 - 查询任务执行完成
2025-06-22 12:32:34 | INFO     | utils.logger:info:61 - 浏览器已退出
2025-06-22 12:32:34 | INFO     | utils.logger:info:61 - 资源清理完成
