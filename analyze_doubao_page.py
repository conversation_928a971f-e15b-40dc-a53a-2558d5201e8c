"""
豆包页面结构分析脚本
实时分析豆包页面的HTML结构，找到正确的响应选择器
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from core.browser_manager import BrowserManager
from utils.logger import app_logger

class DoubaoPageAnalyzer:
    def __init__(self):
        self.logger = app_logger
        self.browser = None
    
    def analyze_page_structure(self):
        """分析豆包页面结构"""
        print("🔍 豆包页面结构分析")
        print("=" * 50)
        
        try:
            # 启动浏览器
            print("🚀 启动浏览器...")
            self.browser = BrowserManager(headless=False, user_data_dir=None)
            
            if not self.browser.start_browser():
                print("❌ 浏览器启动失败")
                return
            
            # 打开豆包网站
            print("🌐 打开豆包网站...")
            tab_handle = self.browser.open_tab("https://www.doubao.com", "豆包分析")
            
            if not tab_handle:
                print("❌ 打开豆包网站失败")
                return
            
            # 等待页面加载
            time.sleep(5)
            
            print("✅ 页面加载完成")
            print("\n💡 请在浏览器中:")
            print("  1. 确保已登录豆包账号")
            print("  2. 发送一条测试消息（如：你好）")
            print("  3. 等待豆包回复")
            print("  4. 然后按回车键继续分析")
            
            input("完成上述步骤后，按回车键开始分析页面结构...")
            
            # 分析页面结构
            self.analyze_elements()
            
            # 保持浏览器打开
            print("\n💡 浏览器保持打开状态，您可以继续手动测试")
            input("按回车键关闭浏览器...")
            
        except Exception as e:
            print(f"❌ 分析过程中出现异常: {e}")
            self.logger.error(f"页面分析异常: {e}")
        finally:
            if self.browser:
                self.browser.quit()
    
    def analyze_elements(self):
        """分析页面元素"""
        print("\n🔍 开始分析页面元素...")
        
        try:
            # 1. 查找所有可能包含消息的元素
            print("\n📋 查找消息相关元素:")
            
            message_selectors = [
                "[data-testid*='message']",
                "[class*='message']",
                "[class*='conversation']", 
                "[class*='chat']",
                "[class*='response']",
                "[class*='content']",
                "[class*='text']",
                "div[data-testid]",
                ".container-jJBCG1"
            ]
            
            for selector in message_selectors:
                try:
                    elements = self.browser.driver.find_elements("css selector", selector)
                    if elements:
                        print(f"✅ {selector}: 找到 {len(elements)} 个元素")
                        
                        # 显示前几个元素的详细信息
                        for i, elem in enumerate(elements[:3]):
                            try:
                                tag_name = elem.tag_name
                                class_name = elem.get_attribute("class") or "无"
                                data_testid = elem.get_attribute("data-testid") or "无"
                                text_preview = elem.text[:50] if elem.text else "无文本"
                                
                                print(f"  [{i+1}] 标签: {tag_name}")
                                print(f"      class: {class_name}")
                                print(f"      data-testid: {data_testid}")
                                print(f"      文本预览: {text_preview}")
                                print()
                            except:
                                continue
                    else:
                        print(f"❌ {selector}: 未找到元素")
                except Exception as e:
                    print(f"❌ {selector}: 查询失败 - {e}")
            
            # 2. 查找所有data-testid属性
            print("\n📋 查找所有data-testid属性:")
            try:
                testid_elements = self.browser.driver.find_elements("css selector", "[data-testid]")
                testids = set()
                
                for elem in testid_elements:
                    testid = elem.get_attribute("data-testid")
                    if testid:
                        testids.add(testid)
                
                print(f"找到 {len(testids)} 个不同的data-testid:")
                for testid in sorted(testids):
                    if any(keyword in testid.lower() for keyword in ['message', 'chat', 'response', 'content', 'text']):
                        print(f"  🎯 {testid}")
                    else:
                        print(f"     {testid}")
                        
            except Exception as e:
                print(f"❌ 查找data-testid失败: {e}")
            
            # 3. 查找包含实际文本内容的元素
            print("\n📋 查找包含长文本的元素:")
            try:
                all_elements = self.browser.driver.find_elements("css selector", "div, p, span")
                text_elements = []
                
                for elem in all_elements:
                    try:
                        text = elem.text.strip()
                        if text and len(text) > 50:  # 只关注长文本
                            class_name = elem.get_attribute("class") or "无"
                            data_testid = elem.get_attribute("data-testid") or "无"
                            text_elements.append({
                                'element': elem,
                                'text': text,
                                'class': class_name,
                                'testid': data_testid,
                                'tag': elem.tag_name
                            })
                    except:
                        continue
                
                # 按文本长度排序，显示最长的几个
                text_elements.sort(key=lambda x: len(x['text']), reverse=True)
                
                print(f"找到 {len(text_elements)} 个包含长文本的元素:")
                for i, elem_info in enumerate(text_elements[:5]):
                    print(f"  [{i+1}] 标签: {elem_info['tag']}")
                    print(f"      class: {elem_info['class']}")
                    print(f"      data-testid: {elem_info['testid']}")
                    print(f"      文本长度: {len(elem_info['text'])} 字符")
                    print(f"      文本预览: {elem_info['text'][:100]}...")
                    print()
                    
            except Exception as e:
                print(f"❌ 查找文本元素失败: {e}")
            
            # 4. 生成建议的选择器
            print("\n💡 建议的选择器配置:")
            
            # 基于分析结果生成选择器建议
            suggested_selectors = []
            
            # 检查是否有特定的data-testid
            try:
                if self.browser.driver.find_elements("css selector", "[data-testid='message_text_content']"):
                    suggested_selectors.append("[data-testid='message_text_content']")
                if self.browser.driver.find_elements("css selector", "[data-testid='receive_message']"):
                    suggested_selectors.append("[data-testid='receive_message']")
                if self.browser.driver.find_elements("css selector", ".container-jJBCG1"):
                    suggested_selectors.append(".container-jJBCG1")
                
                # 添加通用选择器
                suggested_selectors.extend([
                    "[data-testid*='message'][data-testid*='content']",
                    "[class*='message'][class*='content']",
                    "div[data-testid*='message']:not([data-testid*='input'])",
                    ".flow-markdown-body",
                    "[class*='conversation'] [class*='content']"
                ])
                
                print("推荐的output_selector配置:")
                selector_string = ", ".join(suggested_selectors[:5])  # 只取前5个
                print(f'  "output_selector": "{selector_string}"')
                
            except Exception as e:
                print(f"❌ 生成选择器建议失败: {e}")
            
            # 5. 保存分析结果
            try:
                with open("doubao_page_analysis.txt", "w", encoding="utf-8") as f:
                    f.write("豆包页面结构分析结果\n")
                    f.write(f"分析时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write("=" * 50 + "\n\n")
                    
                    # 保存页面源码片段
                    try:
                        page_source = self.browser.driver.page_source
                        f.write("页面源码长度: {} 字符\n\n".format(len(page_source)))
                        
                        # 查找包含豆包回复的部分
                        if "我是由字节跳动开发的人工智能" in page_source:
                            start_idx = page_source.find("我是由字节跳动开发的人工智能") - 500
                            end_idx = start_idx + 2000
                            f.write("包含豆包回复的HTML片段:\n")
                            f.write("-" * 30 + "\n")
                            f.write(page_source[max(0, start_idx):end_idx])
                            f.write("\n" + "-" * 30 + "\n\n")
                    except:
                        pass
                    
                    f.write("建议的选择器:\n")
                    for selector in suggested_selectors:
                        f.write(f"  {selector}\n")
                
                print("📄 详细分析结果已保存到: doubao_page_analysis.txt")
                
            except Exception as e:
                print(f"❌ 保存分析结果失败: {e}")
                
        except Exception as e:
            print(f"❌ 元素分析失败: {e}")

def main():
    """主函数"""
    analyzer = DoubaoPageAnalyzer()
    analyzer.analyze_page_structure()

if __name__ == "__main__":
    main()
