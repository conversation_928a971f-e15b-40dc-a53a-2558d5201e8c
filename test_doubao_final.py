"""
豆包最终测试脚本
使用正确的选择器测试豆包AI的完整交互流程
"""

import asyncio
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from core.task_scheduler import TaskScheduler
from utils.logger import app_logger

async def test_doubao_with_scheduler():
    """使用任务调度器测试豆包"""
    print("🤖 豆包完整功能测试")
    print("=" * 50)
    
    try:
        # 创建任务调度器
        scheduler = TaskScheduler()
        
        # 测试问题
        test_question = "你好，请简单介绍一下自己，不超过100字"
        
        print(f"📝 测试问题: {test_question}")
        print("🚀 开始测试...")
        
        # 只测试豆包模型
        selected_models = ["豆包"]
        
        # 执行查询
        result = await scheduler.run_query(test_question, selected_models)
        
        # 显示结果
        print("\n" + "=" * 50)
        print("📊 测试结果:")
        
        if result.get('success'):
            print("🎉 豆包测试成功!")
            
            analysis = result.get('analysis', {})
            print(f"📈 成功响应: {analysis.get('successful_models', 0)}/{analysis.get('total_models', 0)}")
            
            # 显示豆包的响应
            responses = result.get('summary', {}).get('responses', [])
            for response in responses:
                if response.get('model_name') == '豆包':
                    if response.get('success'):
                        print(f"\n✅ 豆包响应成功:")
                        print(f"📄 响应内容: {response.get('response', '无响应')[:200]}...")
                        print(f"📊 响应长度: {len(response.get('response', ''))} 字符")
                    else:
                        print(f"\n❌ 豆包响应失败:")
                        print(f"🔍 错误信息: {response.get('error', '未知错误')}")
            
            # 显示生成的文件
            output_files = result.get('output_files', {})
            if output_files:
                print(f"\n📁 生成的报告文件:")
                for file_type, file_path in output_files.items():
                    print(f"  {file_type}: {file_path}")
        else:
            print("❌ 豆包测试失败!")
            print(f"🔍 错误信息: {result.get('error', '未知错误')}")
        
        return result.get('success', False)
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现异常: {e}")
        app_logger.error(f"豆包测试异常: {e}")
        return False

async def test_doubao_step_by_step():
    """分步测试豆包功能"""
    print("\n🔍 分步测试豆包功能")
    print("-" * 30)
    
    from core.browser_manager import BrowserManager
    from core.model_handler import ModelHandler
    
    browser = None
    try:
        # 1. 启动浏览器
        print("1️⃣ 启动浏览器...")
        browser = BrowserManager(headless=False, user_data_dir=None)
        
        if not browser.start_browser():
            print("❌ 浏览器启动失败")
            return False
        
        print("✅ 浏览器启动成功")
        
        # 2. 打开豆包网站
        print("\n2️⃣ 打开豆包网站...")
        tab_handle = browser.open_tab("https://www.doubao.com", "豆包")
        
        if not tab_handle:
            print("❌ 打开豆包网站失败")
            return False
        
        print("✅ 成功打开豆包网站")
        
        # 等待页面加载
        await asyncio.sleep(5)
        
        # 3. 检查登录状态
        print("\n3️⃣ 检查登录状态...")
        print("💡 请确保您已经登录豆包账号")
        
        # 等待用户确认
        input("确认已登录后，按回车键继续...")
        
        # 4. 创建模型处理器并测试
        print("\n4️⃣ 测试模型交互...")
        model_handler = ModelHandler(browser)
        
        # 豆包配置
        doubao_config = {
            "name": "豆包",
            "url": "https://www.doubao.com",
            "login_required": True,
            "input_selector": "textarea.semi-input-textarea",
            "submit_selector": "button[aria-label*='发送'], button[title*='发送'], button:has(svg)",
            "output_selector": "[data-testid='message_text_content'], [data-testid='receive_message'], .container-jJBCG1, .flow-markdown-body, [data-testid='message-block-container']",
            "wait_time": 10,
            "description": "字节跳动豆包AI"
        }
        
        # 测试问题
        test_question = "你好，请用一句话介绍自己"
        print(f"📝 发送问题: {test_question}")
        
        # 执行交互
        result = await model_handler.interact_with_model(doubao_config, test_question, tab_handle)
        
        # 显示结果
        print("\n📊 交互结果:")
        if result.get('success'):
            print("🎉 交互成功!")
            response = result.get('response', '')
            print(f"📄 豆包回复: {response}")
            print(f"📊 回复长度: {len(response)} 字符")
            
            # 保存结果
            with open("doubao_test_result.txt", "w", encoding="utf-8") as f:
                f.write(f"豆包测试结果\n")
                f.write(f"时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"问题: {test_question}\n")
                f.write(f"回复: {response}\n")
            
            print("💾 结果已保存到: doubao_test_result.txt")
            
        else:
            print("❌ 交互失败!")
            print(f"🔍 错误信息: {result.get('error', '未知错误')}")
        
        # 保持浏览器打开
        print("\n💡 浏览器保持打开，您可以手动验证结果")
        input("按回车键关闭浏览器...")
        
        return result.get('success', False)
        
    except Exception as e:
        print(f"\n❌ 分步测试异常: {e}")
        app_logger.error(f"分步测试异常: {e}")
        return False
    finally:
        if browser:
            browser.quit()

async def main():
    """主函数"""
    print("🤖 豆包AI模型测试程序")
    print("=" * 60)
    
    print("选择测试模式:")
    print("1. 完整系统测试（推荐）")
    print("2. 分步调试测试")
    
    choice = input("请选择 (1 或 2): ").strip()
    
    if choice == "1":
        print("\n🚀 开始完整系统测试...")
        success = await test_doubao_with_scheduler()
    elif choice == "2":
        print("\n🔍 开始分步调试测试...")
        success = await test_doubao_step_by_step()
    else:
        print("❌ 无效选择")
        return
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 豆包测试完全成功!")
        print("✅ 系统可以正常与豆包AI交互")
        print("✅ 响应获取功能正常")
        print("\n💡 现在您可以使用完整的多标签页查询系统了:")
        print("   python main_new.py")
    else:
        print("⚠️  豆包测试需要进一步调试")
        print("💡 建议:")
        print("  1. 确保豆包账号已正确登录")
        print("  2. 检查网络连接稳定性")
        print("  3. 尝试手动在浏览器中测试豆包")
        print("  4. 如果页面结构有变化，可能需要更新选择器")

if __name__ == "__main__":
    asyncio.run(main())
