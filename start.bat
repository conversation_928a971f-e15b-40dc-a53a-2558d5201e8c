@echo off
chcp 65001 >nul
echo 🤖 多标签页AI模型查询系统
echo ================================

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python，请先安装Python 3.7或更高版本
    pause
    exit /b 1
)

REM 检查是否首次运行
if not exist "output" (
    echo 📦 首次运行，正在进行初始化设置...
    python setup.py
    if errorlevel 1 (
        echo ❌ 初始化失败
        pause
        exit /b 1
    )
    echo.
)

REM 启动程序
echo 🚀 启动多标签页AI模型查询系统...
python main_new.py

pause
