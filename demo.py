"""
演示脚本
展示多标签页AI模型查询系统的基本功能
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from core.task_scheduler import TaskScheduler
from utils.logger import app_logger

async def demo_basic_usage():
    """演示基本使用方法"""
    print("🎯 演示：基本使用方法")
    print("-" * 40)
    
    # 创建任务调度器
    scheduler = TaskScheduler()
    
    # 显示可用模型
    models = scheduler.get_available_models()
    print(f"📋 可用模型: {', '.join(models)}")
    
    # 模拟查询（不实际打开浏览器）
    question = "人工智能的发展趋势是什么？"
    print(f"❓ 示例问题: {question}")
    
    print("💡 在实际使用中，系统会:")
    print("  1. 打开浏览器的多个标签页")
    print("  2. 访问各个AI模型网站")
    print("  3. 自动发送问题并获取回答")
    print("  4. 汇总所有回答生成报告")
    
    print("\n✅ 演示完成!")

async def demo_configuration():
    """演示配置管理"""
    print("\n⚙️ 演示：配置管理")
    print("-" * 40)
    
    scheduler = TaskScheduler()
    
    # 显示当前设置
    print("📊 当前设置:")
    settings = scheduler.config.get('settings', {})
    for key, value in settings.items():
        print(f"  {key}: {value}")
    
    # 演示添加新模型
    print("\n➕ 演示添加新模型配置:")
    demo_model = {
        "name": "演示AI",
        "url": "https://demo-ai.com",
        "login_required": True,
        "input_selector": "textarea",
        "submit_selector": "button[type='submit']",
        "output_selector": ".response",
        "wait_time": 3,
        "description": "演示用AI模型"
    }
    
    print(f"  模型名称: {demo_model['name']}")
    print(f"  网站地址: {demo_model['url']}")
    
    # 添加模型
    success = scheduler.add_model(demo_model)
    print(f"  添加结果: {'成功' if success else '失败'}")
    
    # 显示更新后的模型列表
    updated_models = scheduler.get_available_models()
    print(f"  更新后的模型: {', '.join(updated_models)}")
    
    # 移除演示模型
    success = scheduler.remove_model("演示AI")
    print(f"  移除结果: {'成功' if success else '失败'}")
    
    print("\n✅ 配置管理演示完成!")

def demo_command_line():
    """演示命令行使用方法"""
    print("\n💻 演示：命令行使用方法")
    print("-" * 40)
    
    print("🔧 可用的命令行选项:")
    print()
    
    print("1. 查看可用模型:")
    print("   python main_new.py models")
    print()
    
    print("2. 查询所有模型:")
    print('   python main_new.py query -q "你的问题"')
    print()
    
    print("3. 查询指定模型:")
    print('   python main_new.py query -q "你的问题" -m "腾讯元宝,豆包"')
    print()
    
    print("4. 指定输出格式:")
    print('   python main_new.py query -q "你的问题" -o json')
    print()
    
    print("5. 交互式模式:")
    print("   python main_new.py")
    print("   或")
    print("   python main_new.py interactive")
    print()
    
    print("6. 查看帮助:")
    print("   python main_new.py --help")
    
    print("\n✅ 命令行演示完成!")

def demo_output_files():
    """演示输出文件"""
    print("\n📁 演示：输出文件说明")
    print("-" * 40)
    
    print("📝 系统会在 output/ 目录下生成以下文件:")
    print()
    
    print("1. report_YYYYMMDD_HHMMSS.md")
    print("   - Markdown格式的汇总报告")
    print("   - 包含所有模型的响应和分析")
    print()
    
    print("2. report_YYYYMMDD_HHMMSS.json")
    print("   - JSON格式的结构化数据")
    print("   - 便于程序处理和分析")
    print()
    
    print("3. report_YYYYMMDD_HHMMSS.html")
    print("   - HTML格式的网页报告")
    print("   - 可在浏览器中查看")
    print()
    
    print("4. raw_responses_YYYYMMDD_HHMMSS.json")
    print("   - 原始响应数据")
    print("   - 包含所有详细信息")
    
    print("\n✅ 输出文件演示完成!")

def demo_best_practices():
    """演示最佳实践"""
    print("\n🌟 演示：使用最佳实践")
    print("-" * 40)
    
    print("💡 使用建议:")
    print()
    
    print("1. 首次使用:")
    print("   - 手动登录各个AI模型网站")
    print("   - 确保账号状态正常")
    print("   - 测试单个模型后再批量使用")
    print()
    
    print("2. 问题设计:")
    print("   - 使用清晰、具体的问题")
    print("   - 避免过于复杂的多重问题")
    print("   - 考虑不同模型的特点")
    print()
    
    print("3. 性能优化:")
    print("   - 合理设置并发标签页数量")
    print("   - 根据网络情况调整等待时间")
    print("   - 定期清理输出文件")
    print()
    
    print("4. 故障排除:")
    print("   - 查看日志文件了解详细错误")
    print("   - 检查网站是否更新了页面结构")
    print("   - 确认浏览器和驱动版本匹配")
    
    print("\n✅ 最佳实践演示完成!")

async def main():
    """主演示函数"""
    print("🤖 多标签页AI模型查询系统 - 功能演示")
    print("=" * 60)
    
    try:
        # 运行各种演示
        await demo_basic_usage()
        await demo_configuration()
        demo_command_line()
        demo_output_files()
        demo_best_practices()
        
        print("\n" + "=" * 60)
        print("🎉 所有演示完成!")
        print()
        print("🚀 现在您可以开始使用系统了:")
        print("   python main_new.py")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出错: {e}")
        app_logger.error(f"演示失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
