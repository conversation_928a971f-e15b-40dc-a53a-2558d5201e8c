"""
日志工具模块
提供统一的日志记录功能
"""

import os
import sys
from datetime import datetime
from loguru import logger
from pathlib import Path

class Logger:
    def __init__(self, log_dir="logs", log_level="INFO"):
        """
        初始化日志器
        
        Args:
            log_dir: 日志目录
            log_level: 日志级别
        """
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # 移除默认的控制台输出
        logger.remove()
        
        # 添加控制台输出
        logger.add(
            sys.stdout,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
            level=log_level,
            colorize=True
        )
        
        # 添加文件输出
        log_file = self.log_dir / f"mtabs_{datetime.now().strftime('%Y%m%d')}.log"
        logger.add(
            log_file,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            level=log_level,
            rotation="1 day",
            retention="7 days",
            encoding="utf-8"
        )
        
        # 添加错误日志文件
        error_log_file = self.log_dir / f"mtabs_error_{datetime.now().strftime('%Y%m%d')}.log"
        logger.add(
            error_log_file,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            level="ERROR",
            rotation="1 day",
            retention="30 days",
            encoding="utf-8"
        )
        
        self.logger = logger
    
    def info(self, message):
        """记录信息日志"""
        self.logger.info(message)
    
    def debug(self, message):
        """记录调试日志"""
        self.logger.debug(message)
    
    def warning(self, message):
        """记录警告日志"""
        self.logger.warning(message)
    
    def error(self, message):
        """记录错误日志"""
        self.logger.error(message)
    
    def critical(self, message):
        """记录严重错误日志"""
        self.logger.critical(message)
    
    def exception(self, message):
        """记录异常日志"""
        self.logger.exception(message)

# 创建全局日志器实例
app_logger = Logger()
