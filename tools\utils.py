import platform
import subprocess
import os

def get_default_browser_path():
    os_type = platform.system()
    if os_type == "Windows":
        # 通过注册表获取默认浏览器路径
        try:
            cmd = 'reg query "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\Shell\\Associations\\UrlAssociations\\http\\UserChoice" /v ProgId'
            output = subprocess.check_output(cmd, shell=True, text=True)
            prog_id = output.split()[-1]  # 获取 ProgId（如 "ChromeHTML"）
            # 根据 ProgId 查询具体路径
            cmd = f'reg query "HKEY_CLASSES_ROOT\\{prog_id}\\shell\\open\\command"'
            path_output = subprocess.check_output(cmd, shell=True, text=True)
            return path_output.split('"')[1]  # 提取可执行文件路径
        except Exception:
            return "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe"  # 默认回退
    elif os_type == "Darwin":
        # macOS 通过 `defaults` 命令获取
        try:
            bundle_id = subprocess.check_output(
                "defaults read ~/Library/Preferences/com.apple.LaunchServices/com.apple.launchservices.secure.plist LSHandlers | grep -B 1 'http' | awk -F'\"' '/LSHandlerRoleAll/ {print $2}'",
                shell=True, text=True
            ).strip()
            return f"/Applications/{bundle_id.split('.')[-1]}.app/Contents/MacOS/{bundle_id.split('.')[-1]}"
        except Exception:
            return "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
    elif os_type == "Linux":
        # 通过 xdg-mime 查询默认浏览器
        try:
            return subprocess.check_output("xdg-mime query default x-scheme-handler/http | xargs which", shell=True, text=True).strip()
        except Exception:
            return "/usr/bin/google-chrome"
    else:
        raise OSError("Unsupported OS")

default_browser_path = get_default_browser_path()
print(f"默认浏览器路径: {default_browser_path}")