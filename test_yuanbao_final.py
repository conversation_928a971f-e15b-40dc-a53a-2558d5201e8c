"""
腾讯元宝最终测试脚本
使用更新后的配置测试腾讯元宝的完整交互流程
"""

import asyncio
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from core.task_scheduler import TaskScheduler
from core.browser_manager import BrowserManager
from core.model_handler import ModelHandler
from utils.logger import app_logger

async def test_yuanbao_with_scheduler():
    """使用任务调度器测试腾讯元宝"""
    print("🤖 腾讯元宝完整功能测试")
    print("=" * 50)
    
    try:
        # 创建任务调度器
        scheduler = TaskScheduler()
        
        # 测试问题
        test_question = "你好，请简单介绍一下自己，不超过50字"
        
        print(f"📝 测试问题: {test_question}")
        print("🚀 开始测试...")
        
        # 只测试腾讯元宝模型
        selected_models = ["腾讯元宝"]
        
        # 执行查询
        result = await scheduler.run_query(test_question, selected_models)
        
        # 显示结果
        print("\n" + "=" * 50)
        print("📊 测试结果:")
        
        if result.get('success'):
            print("🎉 腾讯元宝测试成功!")
            
            analysis = result.get('analysis', {})
            print(f"📈 成功响应: {analysis.get('successful_models', 0)}/{analysis.get('total_models', 0)}")
            
            # 显示腾讯元宝的响应
            responses = result.get('summary', {}).get('responses', [])
            for response in responses:
                if response.get('model_name') == '腾讯元宝':
                    if response.get('success'):
                        print(f"\n✅ 腾讯元宝响应成功:")
                        print(f"📄 响应内容: {response.get('response', '无响应')[:200]}...")
                        print(f"📊 响应长度: {len(response.get('response', ''))} 字符")
                    else:
                        print(f"\n❌ 腾讯元宝响应失败:")
                        print(f"🔍 错误信息: {response.get('error', '未知错误')}")
            
            # 显示生成的文件
            output_files = result.get('output_files', {})
            if output_files:
                print(f"\n📁 生成的报告文件:")
                for file_type, file_path in output_files.items():
                    print(f"  {file_type}: {file_path}")
        else:
            print("❌ 腾讯元宝测试失败!")
            print(f"🔍 错误信息: {result.get('error', '未知错误')}")
        
        return result.get('success', False)
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现异常: {e}")
        app_logger.error(f"腾讯元宝测试异常: {e}")
        return False

async def test_yuanbao_step_by_step():
    """分步测试腾讯元宝功能"""
    print("\n🔍 分步测试腾讯元宝功能")
    print("-" * 30)
    
    browser = None
    try:
        # 1. 启动浏览器
        print("1️⃣ 启动浏览器...")
        browser = BrowserManager(headless=False, user_data_dir=None)
        
        if not browser.start_browser():
            print("❌ 浏览器启动失败")
            return False
        
        print("✅ 浏览器启动成功")
        
        # 2. 打开腾讯元宝网站
        print("\n2️⃣ 打开腾讯元宝网站...")
        tab_handle = browser.open_tab("https://yuanbao.tencent.com", "腾讯元宝")
        
        if not tab_handle:
            print("❌ 打开腾讯元宝网站失败")
            return False
        
        print("✅ 成功打开腾讯元宝网站")
        
        # 等待页面加载
        await asyncio.sleep(5)
        
        # 3. 检查登录状态
        print("\n3️⃣ 检查登录状态...")
        print("💡 请确保您已经登录腾讯元宝账号")
        
        # 等待用户确认登录
        input("确认已登录后，按回车键继续...")
        
        # 4. 测试输入框
        print("\n4️⃣ 测试输入框...")
        input_element = browser.wait_for_element("[contenteditable='true']", timeout=10)
        
        if input_element:
            print("✅ 找到输入框")
            
            # 测试输入
            test_text = "你好，请简单介绍一下自己"
            print(f"📝 输入测试文本: {test_text}")
            
            # 清空并输入文本
            input_element.clear()
            input_element.send_keys(test_text)
            print("✅ 文本输入成功")
            
            # 等待一下
            await asyncio.sleep(2)
            
            # 5. 测试发送按钮
            print("\n5️⃣ 测试发送按钮...")
            
            # 尝试多种发送方式
            send_success = False
            
            # 方式1: 尝试点击submit按钮
            try:
                submit_button = browser.wait_for_element("button[type='submit']", timeout=5)
                if submit_button and submit_button.is_enabled():
                    submit_button.click()
                    print("✅ 通过submit按钮发送成功")
                    send_success = True
                else:
                    print("⚠️  submit按钮不可用")
            except Exception as e:
                print(f"⚠️  submit按钮点击失败: {e}")
            
            # 方式2: 如果按钮点击失败，尝试按回车键
            if not send_success:
                try:
                    from selenium.webdriver.common.keys import Keys
                    input_element.send_keys(Keys.RETURN)
                    print("✅ 通过回车键发送成功")
                    send_success = True
                except Exception as e:
                    print(f"⚠️  回车键发送失败: {e}")
            
            # 方式3: 尝试Ctrl+Enter
            if not send_success:
                try:
                    from selenium.webdriver.common.keys import Keys
                    input_element.send_keys(Keys.CONTROL + Keys.RETURN)
                    print("✅ 通过Ctrl+Enter发送成功")
                    send_success = True
                except Exception as e:
                    print(f"⚠️  Ctrl+Enter发送失败: {e}")
            
            if send_success:
                # 6. 等待响应
                print("\n6️⃣ 等待腾讯元宝响应...")
                await asyncio.sleep(10)
                
                # 7. 尝试获取响应
                print("\n7️⃣ 获取响应...")
                
                response_selectors = [
                    ".agent-chat__list",
                    "[class*='chat']",
                    ".message-content",
                    "[class*='response']"
                ]
                
                response_found = False
                response_text = ""
                
                for selector in response_selectors:
                    try:
                        elements = browser.driver.find_elements("css selector", selector)
                        if elements:
                            for element in elements[-3:]:  # 检查最后3个元素
                                text = element.text.strip()
                                if text and len(text) > 20 and "你好" not in text:  # 排除我们自己的输入
                                    response_text = text
                                    response_found = True
                                    break
                            if response_found:
                                break
                    except:
                        continue
                
                if response_found:
                    print("🎉 成功获取到腾讯元宝的响应!")
                    print(f"📄 响应内容: {response_text[:200]}...")
                    print(f"📊 响应长度: {len(response_text)} 字符")
                    
                    # 保存结果
                    with open("yuanbao_test_result.txt", "w", encoding="utf-8") as f:
                        f.write(f"腾讯元宝测试结果\n")
                        f.write(f"时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                        f.write(f"问题: {test_text}\n")
                        f.write(f"回复: {response_text}\n")
                    
                    print("💾 结果已保存到: yuanbao_test_result.txt")
                    
                else:
                    print("❌ 未能获取到腾讯元宝的响应")
                    print("💡 可能需要等待更长时间或检查响应选择器")
            else:
                print("❌ 发送消息失败")
                return False
        else:
            print("❌ 未找到输入框")
            return False
        
        # 保持浏览器打开
        print("\n💡 浏览器保持打开，您可以手动验证结果")
        input("按回车键关闭浏览器...")
        
        return response_found if 'response_found' in locals() else False
        
    except Exception as e:
        print(f"\n❌ 分步测试异常: {e}")
        app_logger.error(f"分步测试异常: {e}")
        return False
    finally:
        if browser:
            browser.quit()

async def main():
    """主函数"""
    print("🤖 腾讯元宝AI模型测试程序")
    print("=" * 60)
    
    print("选择测试模式:")
    print("1. 完整系统测试（推荐）")
    print("2. 分步调试测试")
    
    choice = input("请选择 (1 或 2): ").strip()
    
    if choice == "1":
        print("\n🚀 开始完整系统测试...")
        success = await test_yuanbao_with_scheduler()
    elif choice == "2":
        print("\n🔍 开始分步调试测试...")
        success = await test_yuanbao_step_by_step()
    else:
        print("❌ 无效选择")
        return
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 腾讯元宝测试完全成功!")
        print("✅ 系统可以正常与腾讯元宝交互")
        print("✅ 响应获取功能正常")
        print("\n💡 现在您可以使用完整的多标签页查询系统了:")
        print("   python main_new.py")
    else:
        print("⚠️  腾讯元宝测试需要进一步调试")
        print("💡 建议:")
        print("  1. 确保腾讯元宝账号已正确登录")
        print("  2. 检查网络连接稳定性")
        print("  3. 尝试手动在浏览器中测试腾讯元宝")
        print("  4. 如果页面结构有变化，可能需要更新选择器")

if __name__ == "__main__":
    asyncio.run(main())
