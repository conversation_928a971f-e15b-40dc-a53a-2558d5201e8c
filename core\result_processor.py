"""
结果处理模块
负责汇总和加工AI模型的响应结果
"""

import json
import time
from datetime import datetime
from typing import List, Dict, Optional
from pathlib import Path
import markdown
from utils.logger import app_logger

class ResultProcessor:
    def __init__(self, output_dir: str = "output"):
        """
        初始化结果处理器
        
        Args:
            output_dir: 输出目录
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.logger = app_logger
        
    def process_results(self, results: List[Dict], question: str, output_format: str = "markdown") -> Dict:
        """
        处理和汇总结果
        
        Args:
            results: AI模型响应结果列表
            question: 原始问题
            output_format: 输出格式 (markdown, json, html)
            
        Returns:
            处理后的结果信息
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 分析结果
            analysis = self._analyze_results(results)
            
            # 生成汇总报告
            summary = self._generate_summary(results, question, analysis)
            
            # 保存结果
            output_files = {}
            
            if output_format.lower() in ["markdown", "md"]:
                md_file = self._save_markdown_report(summary, timestamp)
                output_files['markdown'] = md_file
                
            if output_format.lower() in ["json"]:
                json_file = self._save_json_report(results, summary, timestamp)
                output_files['json'] = json_file
                
            if output_format.lower() in ["html"]:
                html_file = self._save_html_report(summary, timestamp)
                output_files['html'] = html_file
            
            # 保存原始响应
            raw_file = self._save_raw_responses(results, timestamp)
            output_files['raw'] = raw_file
            
            self.logger.info(f"结果处理完成，生成文件: {list(output_files.values())}")
            
            return {
                'success': True,
                'summary': summary,
                'analysis': analysis,
                'output_files': output_files,
                'timestamp': timestamp
            }
            
        except Exception as e:
            self.logger.error(f"结果处理失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().strftime("%Y%m%d_%H%M%S")
            }
    
    def _analyze_results(self, results: List[Dict]) -> Dict:
        """分析结果统计信息"""
        total_models = len(results)
        successful_models = len([r for r in results if r.get('success', False)])
        failed_models = total_models - successful_models
        
        response_lengths = []
        for result in results:
            if result.get('response'):
                response_lengths.append(len(result['response']))
        
        avg_response_length = sum(response_lengths) / len(response_lengths) if response_lengths else 0
        
        return {
            'total_models': total_models,
            'successful_models': successful_models,
            'failed_models': failed_models,
            'success_rate': successful_models / total_models if total_models > 0 else 0,
            'avg_response_length': avg_response_length,
            'response_lengths': response_lengths
        }
    
    def _generate_summary(self, results: List[Dict], question: str, analysis: Dict) -> Dict:
        """生成汇总报告"""
        return {
            'question': question,
            'timestamp': datetime.now().isoformat(),
            'analysis': analysis,
            'responses': results,
            'consolidated_insights': self._extract_insights(results)
        }
    
    def _extract_insights(self, results: List[Dict]) -> List[str]:
        """从所有响应中提取关键洞察"""
        insights = []
        
        # 这里可以实现更复杂的文本分析逻辑
        # 目前简单地收集所有成功的响应
        for result in results:
            if result.get('success') and result.get('response'):
                response = result['response']
                # 简单的关键信息提取
                if len(response) > 100:
                    # 取前200个字符作为摘要
                    summary = response[:200] + "..." if len(response) > 200 else response
                    insights.append(f"**{result['model_name']}**: {summary}")
        
        return insights
    
    def _save_markdown_report(self, summary: Dict, timestamp: str) -> str:
        """保存Markdown格式报告"""
        filename = f"report_{timestamp}.md"
        filepath = self.output_dir / filename
        
        md_content = f"""# AI模型查询报告

## 查询信息
- **问题**: {summary['question']}
- **时间**: {summary['timestamp']}
- **查询模型数**: {summary['analysis']['total_models']}
- **成功响应数**: {summary['analysis']['successful_models']}
- **成功率**: {summary['analysis']['success_rate']:.2%}

## 模型响应汇总

"""
        
        for result in summary['responses']:
            md_content += f"### {result['model_name']}\n\n"
            if result.get('success'):
                md_content += f"**状态**: ✅ 成功\n\n"
                md_content += f"**响应内容**:\n\n{result['response']}\n\n"
            else:
                md_content += f"**状态**: ❌ 失败\n\n"
                md_content += f"**错误信息**: {result.get('error', '未知错误')}\n\n"
            
            md_content += "---\n\n"
        
        # 添加关键洞察
        if summary['consolidated_insights']:
            md_content += "## 关键洞察\n\n"
            for insight in summary['consolidated_insights']:
                md_content += f"- {insight}\n\n"
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(md_content)
        
        return str(filepath)
    
    def _save_json_report(self, results: List[Dict], summary: Dict, timestamp: str) -> str:
        """保存JSON格式报告"""
        filename = f"report_{timestamp}.json"
        filepath = self.output_dir / filename
        
        json_data = {
            'metadata': {
                'timestamp': timestamp,
                'question': summary['question'],
                'analysis': summary['analysis']
            },
            'results': results,
            'summary': summary
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2)
        
        return str(filepath)
    
    def _save_html_report(self, summary: Dict, timestamp: str) -> str:
        """保存HTML格式报告"""
        filename = f"report_{timestamp}.html"
        filepath = self.output_dir / filename
        
        # 先生成markdown内容
        md_filename = f"temp_{timestamp}.md"
        md_filepath = self._save_markdown_report(summary, f"temp_{timestamp}")
        
        # 转换为HTML
        with open(md_filepath, 'r', encoding='utf-8') as f:
            md_content = f.read()
        
        html_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI模型查询报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }}
        h1, h2, h3 {{ color: #333; }}
        .success {{ color: #28a745; }}
        .error {{ color: #dc3545; }}
        .summary {{ background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }}
        pre {{ background: #f4f4f4; padding: 15px; border-radius: 5px; overflow-x: auto; }}
        hr {{ margin: 30px 0; }}
    </style>
</head>
<body>
{markdown.markdown(md_content, extensions=['tables', 'fenced_code'])}
</body>
</html>"""
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        # 删除临时markdown文件
        Path(md_filepath).unlink(missing_ok=True)
        
        return str(filepath)
    
    def _save_raw_responses(self, results: List[Dict], timestamp: str) -> str:
        """保存原始响应数据"""
        filename = f"raw_responses_{timestamp}.json"
        filepath = self.output_dir / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        return str(filepath)
    
    def get_latest_report(self, format_type: str = "markdown") -> Optional[str]:
        """获取最新的报告文件路径"""
        pattern = f"report_*.{format_type}"
        files = list(self.output_dir.glob(pattern))
        if files:
            return str(max(files, key=lambda x: x.stat().st_mtime))
        return None
