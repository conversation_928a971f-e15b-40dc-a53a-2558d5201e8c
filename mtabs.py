from browser_use import Agent
from langchain_openai import ChatOpenAI
import asyncio

async def main():
    agent = Agent(
        task="""
        1. 打开 yuanbao.tencent.com，用我的账号登录（记住密码）
        2. 输入提示词：“写一段关于新能源汽车的营销文案 用 Markdown 表格展示”     
        3. 打开新标签页，访问 www.doubao.com，输入提示词：“新能源汽车的三大趋势，用 Markdown 表格展示”   
        4. 将所有结果保存到本地 report.md
        """,
        llm=ChatOpenAI(model="deepseek"),  # 用
        use_vision=True  # 启用视觉识别
    )
    await agent.run()

asyncio.run(main())