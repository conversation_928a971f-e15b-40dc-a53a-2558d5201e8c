"""
快速设置脚本
帮助用户快速配置和启动多标签页AI模型查询系统
"""

import os
import sys
import subprocess
from pathlib import Path

def print_banner():
    """打印欢迎横幅"""
    print("🤖 多标签页AI模型查询系统 - 快速设置")
    print("=" * 50)

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        print("❌ 需要Python 3.7或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    else:
        print(f"✅ Python版本检查通过: {sys.version.split()[0]}")
        return True

def install_dependencies():
    """安装依赖包"""
    print("\n📦 安装依赖包...")
    
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("❌ 找不到requirements.txt文件")
        return False
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ 依赖包安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖包安装失败: {e}")
        return False

def create_directories():
    """创建必要的目录"""
    print("\n📁 创建目录结构...")
    
    directories = ["output", "logs"]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ 创建目录: {directory}")
    
    return True

def check_config_files():
    """检查配置文件"""
    print("\n⚙️ 检查配置文件...")
    
    config_file = Path("config/models.json")
    if config_file.exists():
        print("✅ 配置文件存在")
        return True
    else:
        print("❌ 配置文件不存在")
        print("请确保 config/models.json 文件存在")
        return False

def run_system_test():
    """运行系统测试"""
    print("\n🧪 运行系统测试...")
    
    try:
        result = subprocess.run([
            sys.executable, "test_system.py"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 系统测试通过")
            return True
        else:
            print("❌ 系统测试失败")
            print("错误输出:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ 运行测试时出错: {e}")
        return False

def show_usage_instructions():
    """显示使用说明"""
    print("\n📖 使用说明:")
    print("-" * 30)
    print("1. 交互式模式:")
    print("   python main_new.py")
    print()
    print("2. 命令行模式:")
    print("   python main_new.py query -q \"你的问题\"")
    print()
    print("3. 查看帮助:")
    print("   python main_new.py --help")
    print()
    print("4. 运行示例:")
    print("   python example.py")

def main():
    """主函数"""
    print_banner()
    
    # 检查Python版本
    if not check_python_version():
        return False
    
    # 安装依赖
    if not install_dependencies():
        return False
    
    # 创建目录
    if not create_directories():
        return False
    
    # 检查配置文件
    if not check_config_files():
        return False
    
    # 运行系统测试
    if not run_system_test():
        print("⚠️  系统测试失败，但您仍可以尝试运行程序")
    
    # 显示使用说明
    show_usage_instructions()
    
    print("\n🎉 设置完成!")
    print("现在您可以开始使用多标签页AI模型查询系统了")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ 设置过程中遇到问题")
        print("请检查错误信息并手动解决相关问题")
        sys.exit(1)
