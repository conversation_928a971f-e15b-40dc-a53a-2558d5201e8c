"""
任务调度模块
负责协调整个查询流程
"""

import asyncio
import json
from typing import List, Dict, Optional
from pathlib import Path
from core.browser_manager import BrowserManager
from core.model_handler import ModelHandler
from core.result_processor import ResultProcessor
from utils.logger import app_logger

class TaskScheduler:
    def __init__(self, config_path: str = "config/models.json"):
        """
        初始化任务调度器

        Args:
            config_path: 配置文件路径
        """
        self.config_path = Path(config_path)
        self.logger = app_logger  # 先初始化logger
        self.config = self._load_config()
        self.browser_manager = None
        self.model_handler = None
        self.result_processor = None

    def _load_config(self) -> Dict:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            self.logger.info(f"配置文件加载成功: {self.config_path}")
            return config
        except Exception as e:
            self.logger.error(f"配置文件加载失败: {e}")
            return self._get_default_config()

    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            "ai_models": [],
            "settings": {
                "max_concurrent_tabs": 3,
                "default_wait_time": 10,
                "retry_attempts": 3,
                "output_format": "markdown",
                "browser_headless": False
            }
        }

    async def run_query(self, question: str, selected_models: Optional[List[str]] = None) -> Dict:
        """
        运行查询任务

        Args:
            question: 要查询的问题
            selected_models: 选择的模型列表，如果为None则使用所有模型

        Returns:
            查询结果
        """
        try:
            self.logger.info(f"开始执行查询任务: {question}")

            # 初始化组件
            await self._initialize_components()

            # 获取要使用的模型配置
            models_to_use = self._get_models_to_use(selected_models)
            if not models_to_use:
                return {
                    'success': False,
                    'error': '没有可用的模型配置'
                }

            # 打开浏览器标签页
            tab_handles = await self._open_model_tabs(models_to_use)
            if not tab_handles:
                return {
                    'success': False,
                    'error': '无法打开任何模型标签页'
                }

            # 执行查询
            results = await self._execute_queries(models_to_use, question, tab_handles)

            # 处理结果
            output_format = self.config['settings'].get('output_format', 'markdown')
            processed_result = self.result_processor.process_results(results, question, output_format)

            self.logger.info("查询任务执行完成")
            return processed_result

        except Exception as e:
            self.logger.error(f"查询任务执行失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
        finally:
            await self._cleanup()

    async def _initialize_components(self):
        """初始化组件"""
        # 初始化浏览器管理器
        settings = self.config.get('settings', {})
        self.browser_manager = BrowserManager(
            headless=settings.get('browser_headless', False),
            user_data_dir=settings.get('user_data_dir')
        )

        # 启动浏览器
        if not self.browser_manager.start_browser():
            raise Exception("浏览器启动失败")

        # 初始化模型处理器
        self.model_handler = ModelHandler(self.browser_manager)

        # 初始化结果处理器
        self.result_processor = ResultProcessor()

        self.logger.info("组件初始化完成")

    def _get_models_to_use(self, selected_models: Optional[List[str]]) -> List[Dict]:
        """获取要使用的模型配置"""
        all_models = self.config.get('ai_models', [])

        if not selected_models:
            return all_models

        return [model for model in all_models if model['name'] in selected_models]

    async def _open_model_tabs(self, models: List[Dict]) -> Dict[str, str]:
        """打开模型标签页"""
        tab_handles = {}
        max_concurrent = self.config['settings'].get('max_concurrent_tabs', 5)

        # 限制并发打开的标签页数量
        models_to_open = models[:max_concurrent]

        for model in models_to_open:
            try:
                tab_handle = self.browser_manager.open_tab(model['url'], model['name'])
                if tab_handle:
                    tab_handles[model['name']] = tab_handle
                    self.logger.info(f"成功打开 {model['name']} 标签页")
                    # 等待一下再打开下一个标签页
                    await asyncio.sleep(2)
                else:
                    self.logger.warning(f"打开 {model['name']} 标签页失败")
            except Exception as e:
                self.logger.error(f"打开 {model['name']} 标签页时出错: {e}")

        return tab_handles

    async def _execute_queries(self, models: List[Dict], question: str, tab_handles: Dict[str, str]) -> List[Dict]:
        """执行查询"""
        # 过滤出有效的模型配置
        valid_models = [model for model in models if model['name'] in tab_handles]

        if not valid_models:
            return []

        # 批量执行查询
        results = await self.model_handler.batch_interact(valid_models, question, tab_handles)

        return results

    async def _cleanup(self):
        """清理资源"""
        try:
            if self.browser_manager:
                self.browser_manager.quit()
            self.logger.info("资源清理完成")
        except Exception as e:
            self.logger.error(f"资源清理失败: {e}")

    def get_available_models(self) -> List[str]:
        """获取可用的模型列表"""
        return [model['name'] for model in self.config.get('ai_models', [])]

    def add_model(self, model_config: Dict) -> bool:
        """添加新的模型配置"""
        try:
            self.config['ai_models'].append(model_config)
            self._save_config()
            self.logger.info(f"添加模型配置成功: {model_config['name']}")
            return True
        except Exception as e:
            self.logger.error(f"添加模型配置失败: {e}")
            return False

    def remove_model(self, model_name: str) -> bool:
        """移除模型配置"""
        try:
            original_count = len(self.config['ai_models'])
            self.config['ai_models'] = [
                model for model in self.config['ai_models']
                if model['name'] != model_name
            ]

            if len(self.config['ai_models']) < original_count:
                self._save_config()
                self.logger.info(f"移除模型配置成功: {model_name}")
                return True
            else:
                self.logger.warning(f"未找到模型配置: {model_name}")
                return False
        except Exception as e:
            self.logger.error(f"移除模型配置失败: {e}")
            return False

    def _save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")

    def update_settings(self, new_settings: Dict) -> bool:
        """更新设置"""
        try:
            self.config['settings'].update(new_settings)
            self._save_config()
            self.logger.info("设置更新成功")
            return True
        except Exception as e:
            self.logger.error(f"设置更新失败: {e}")
            return False
