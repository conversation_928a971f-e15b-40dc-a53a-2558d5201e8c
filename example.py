"""
使用示例脚本
演示如何使用多标签页AI模型查询系统
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from core.task_scheduler import TaskScheduler
from utils.logger import app_logger

async def example_basic_query():
    """基础查询示例"""
    print("🔍 基础查询示例")
    print("-" * 30)
    
    scheduler = TaskScheduler()
    
    # 查询问题
    question = "请介绍一下人工智能在医疗领域的应用"
    
    print(f"问题: {question}")
    print("正在查询...")
    
    # 执行查询
    result = await scheduler.run_query(question)
    
    if result.get('success'):
        print("✅ 查询成功!")
        print(f"成功响应数: {result['analysis']['successful_models']}")
        print(f"报告文件: {list(result['output_files'].values())}")
    else:
        print(f"❌ 查询失败: {result.get('error')}")

async def example_selective_query():
    """选择性查询示例"""
    print("\n🎯 选择性查询示例")
    print("-" * 30)
    
    scheduler = TaskScheduler()
    
    # 只查询特定模型
    selected_models = ["腾讯元宝", "豆包"]
    question = "区块链技术的优缺点分析"
    
    print(f"问题: {question}")
    print(f"选择的模型: {', '.join(selected_models)}")
    print("正在查询...")
    
    # 执行查询
    result = await scheduler.run_query(question, selected_models)
    
    if result.get('success'):
        print("✅ 查询成功!")
        print(f"成功响应数: {result['analysis']['successful_models']}")
        
        # 显示部分结果
        for response in result['summary']['responses']:
            if response.get('success'):
                print(f"\n📝 {response['model_name']}:")
                print(f"   {response['response'][:100]}...")
    else:
        print(f"❌ 查询失败: {result.get('error')}")

async def example_model_management():
    """模型管理示例"""
    print("\n⚙️ 模型管理示例")
    print("-" * 30)
    
    scheduler = TaskScheduler()
    
    # 显示可用模型
    models = scheduler.get_available_models()
    print(f"可用模型: {', '.join(models)}")
    
    # 添加新模型配置示例
    new_model = {
        "name": "示例AI",
        "url": "https://example-ai.com",
        "login_required": True,
        "input_selector": "textarea",
        "submit_selector": "button[type='submit']",
        "output_selector": ".response",
        "wait_time": 3,
        "description": "示例AI模型"
    }
    
    print(f"添加新模型: {new_model['name']}")
    success = scheduler.add_model(new_model)
    print(f"添加结果: {'成功' if success else '失败'}")
    
    # 更新后的模型列表
    updated_models = scheduler.get_available_models()
    print(f"更新后的模型: {', '.join(updated_models)}")
    
    # 移除示例模型
    print(f"移除示例模型: {new_model['name']}")
    success = scheduler.remove_model(new_model['name'])
    print(f"移除结果: {'成功' if success else '失败'}")

async def example_settings_update():
    """设置更新示例"""
    print("\n🔧 设置更新示例")
    print("-" * 30)
    
    scheduler = TaskScheduler()
    
    # 更新设置
    new_settings = {
        "max_concurrent_tabs": 3,
        "default_wait_time": 15,
        "output_format": "json",
        "browser_headless": False
    }
    
    print("更新设置...")
    success = scheduler.update_settings(new_settings)
    print(f"更新结果: {'成功' if success else '失败'}")
    
    if success:
        print("新设置:")
        for key, value in new_settings.items():
            print(f"  {key}: {value}")

async def main():
    """主函数"""
    print("🤖 多标签页AI模型查询系统 - 使用示例")
    print("=" * 50)
    
    try:
        # 运行各种示例
        await example_basic_query()
        await example_selective_query()
        await example_model_management()
        await example_settings_update()
        
        print("\n🎉 所有示例执行完成!")
        
    except Exception as e:
        print(f"\n❌ 示例执行失败: {e}")
        app_logger.error(f"示例执行失败: {e}")

if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())
