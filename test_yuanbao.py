"""
腾讯元宝测试脚本
专门测试腾讯元宝AI的三个关键选择器：input_selector, submit_selector, output_selector
"""

import asyncio
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from core.browser_manager import BrowserManager
from core.model_handler import ModelHandler
from utils.logger import app_logger

class YuanbaoTester:
    def __init__(self):
        self.logger = app_logger
        self.browser = None
        
        # 腾讯元宝当前配置
        self.yuanbao_config = {
            "name": "腾讯元宝",
            "url": "https://yuanbao.tencent.com",
            "login_required": True,
            "input_selector": "textarea[placeholder*='输入']",
            "submit_selector": "button[type='submit']",
            "output_selector": ".message-content",
            "wait_time": 3,
            "description": "腾讯元宝AI助手"
        }
    
    async def analyze_yuanbao_page(self):
        """分析腾讯元宝页面结构"""
        print("🔍 腾讯元宝页面结构分析")
        print("=" * 50)
        
        try:
            # 启动浏览器
            print("🚀 启动浏览器...")
            self.browser = BrowserManager(headless=False, user_data_dir=None)
            
            if not self.browser.start_browser():
                print("❌ 浏览器启动失败")
                return False
            
            print("✅ 浏览器启动成功")
            
            # 打开腾讯元宝网站
            print("\n🌐 打开腾讯元宝网站...")
            tab_handle = self.browser.open_tab(self.yuanbao_config['url'], self.yuanbao_config['name'])
            
            if not tab_handle:
                print("❌ 打开腾讯元宝网站失败")
                return False
            
            print("✅ 成功打开腾讯元宝网站")
            
            # 等待页面加载
            await asyncio.sleep(5)
            
            # 获取页面基本信息
            try:
                title = self.browser.driver.title
                url = self.browser.driver.current_url
                print(f"📄 页面标题: {title}")
                print(f"🔗 当前URL: {url}")
            except Exception as e:
                print(f"⚠️  获取页面信息失败: {e}")
            
            print("\n💡 请在浏览器中:")
            print("  1. 确保已登录腾讯元宝账号")
            print("  2. 如果看到登录页面，请先登录")
            print("  3. 然后按回车键继续分析页面元素")
            
            input("完成登录后，按回车键继续...")
            
            # 分析页面元素
            await self.analyze_page_elements()
            
            return True
            
        except Exception as e:
            print(f"❌ 分析过程中出现异常: {e}")
            self.logger.error(f"腾讯元宝页面分析异常: {e}")
            return False
    
    async def analyze_page_elements(self):
        """分析页面元素"""
        print("\n🔍 开始分析页面元素...")
        
        # 1. 分析输入框
        print("\n📝 分析输入框 (input_selector):")
        print(f"当前配置: {self.yuanbao_config['input_selector']}")
        
        input_selectors = [
            "textarea[placeholder*='输入']",
            "textarea[placeholder*='请输入']",
            "textarea",
            "input[type='text']",
            "[contenteditable='true']",
            ".input-area textarea",
            "#chat-input"
        ]
        
        input_found = False
        best_input_selector = None
        
        for selector in input_selectors:
            try:
                elements = self.browser.driver.find_elements("css selector", selector)
                if elements:
                    print(f"✅ {selector}: 找到 {len(elements)} 个元素")
                    for i, elem in enumerate(elements[:2]):
                        try:
                            placeholder = elem.get_attribute("placeholder") or "无"
                            class_name = elem.get_attribute("class") or "无"
                            print(f"  [{i+1}] placeholder: {placeholder}")
                            print(f"      class: {class_name}")
                            
                            # 检查是否是主要的输入框
                            if not input_found and ("输入" in placeholder or elem.is_displayed()):
                                input_found = True
                                best_input_selector = selector
                        except:
                            continue
                else:
                    print(f"❌ {selector}: 未找到元素")
            except Exception as e:
                print(f"❌ {selector}: 查询失败 - {e}")
        
        # 2. 分析发送按钮
        print("\n🔘 分析发送按钮 (submit_selector):")
        print(f"当前配置: {self.yuanbao_config['submit_selector']}")
        
        submit_selectors = [
            "button[type='submit']",
            "button[aria-label*='发送']",
            "button[title*='发送']",
            "button:contains('发送')",
            ".send-button",
            ".submit-button",
            "button[data-testid*='send']",
            "button.send-btn"
        ]
        
        submit_found = False
        best_submit_selector = None
        
        for selector in submit_selectors:
            try:
                elements = self.browser.driver.find_elements("css selector", selector)
                if elements:
                    print(f"✅ {selector}: 找到 {len(elements)} 个元素")
                    for i, elem in enumerate(elements[:2]):
                        try:
                            text = elem.text.strip()
                            aria_label = elem.get_attribute("aria-label") or "无"
                            title = elem.get_attribute("title") or "无"
                            type_attr = elem.get_attribute("type") or "无"
                            print(f"  [{i+1}] text: '{text}'")
                            print(f"      aria-label: {aria_label}")
                            print(f"      title: {title}")
                            print(f"      type: {type_attr}")
                            
                            # 检查是否是发送按钮
                            if not submit_found and (
                                "发送" in text or "发送" in aria_label or 
                                "发送" in title or type_attr == "submit"
                            ):
                                submit_found = True
                                best_submit_selector = selector
                        except:
                            continue
                else:
                    print(f"❌ {selector}: 未找到元素")
            except Exception as e:
                print(f"❌ {selector}: 查询失败 - {e}")
        
        # 3. 分析响应输出区域
        print("\n📄 分析响应输出区域 (output_selector):")
        print(f"当前配置: {self.yuanbao_config['output_selector']}")
        
        output_selectors = [
            ".message-content",
            "[class*='message']",
            "[class*='response']",
            "[class*='chat']",
            "[class*='conversation']",
            "[data-testid*='message']",
            ".chat-message",
            ".response-content",
            ".ai-response"
        ]
        
        output_found = False
        best_output_selector = None
        
        for selector in output_selectors:
            try:
                elements = self.browser.driver.find_elements("css selector", selector)
                if elements:
                    print(f"✅ {selector}: 找到 {len(elements)} 个元素")
                    
                    # 检查元素是否包含实际的文本内容
                    for i, elem in enumerate(elements[:3]):
                        try:
                            text = elem.text.strip()
                            class_name = elem.get_attribute("class") or "无"
                            data_testid = elem.get_attribute("data-testid") or "无"
                            print(f"  [{i+1}] class: {class_name}")
                            print(f"      data-testid: {data_testid}")
                            print(f"      文本长度: {len(text)} 字符")
                            if text:
                                print(f"      文本预览: {text[:50]}...")
                                
                                # 如果包含较长的文本，可能是响应区域
                                if not output_found and len(text) > 20:
                                    output_found = True
                                    best_output_selector = selector
                        except:
                            continue
                else:
                    print(f"❌ {selector}: 未找到元素")
            except Exception as e:
                print(f"❌ {selector}: 查询失败 - {e}")
        
        # 4. 生成建议配置
        print("\n💡 建议的选择器配置:")
        
        suggested_config = {
            "input_selector": best_input_selector or self.yuanbao_config['input_selector'],
            "submit_selector": best_submit_selector or self.yuanbao_config['submit_selector'],
            "output_selector": best_output_selector or self.yuanbao_config['output_selector']
        }
        
        print("推荐配置:")
        for key, value in suggested_config.items():
            current_value = self.yuanbao_config[key]
            status = "✅ 保持" if value == current_value else "🔄 建议更新"
            print(f"  {key}: \"{value}\" ({status})")
        
        # 5. 保存分析结果
        try:
            with open("yuanbao_analysis.txt", "w", encoding="utf-8") as f:
                f.write("腾讯元宝页面分析结果\n")
                f.write(f"分析时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("=" * 50 + "\n\n")
                
                f.write("当前配置:\n")
                for key, value in self.yuanbao_config.items():
                    f.write(f"  {key}: {value}\n")
                
                f.write("\n建议配置:\n")
                for key, value in suggested_config.items():
                    f.write(f"  {key}: {value}\n")
                
                f.write(f"\n输入框找到: {input_found}\n")
                f.write(f"发送按钮找到: {submit_found}\n")
                f.write(f"输出区域找到: {output_found}\n")
            
            print("📄 详细分析结果已保存到: yuanbao_analysis.txt")
            
        except Exception as e:
            print(f"❌ 保存分析结果失败: {e}")
        
        return suggested_config
    
    async def test_interaction(self):
        """测试完整交互流程"""
        print("\n🧪 测试完整交互流程...")
        
        try:
            # 创建模型处理器
            model_handler = ModelHandler(self.browser)
            
            # 测试问题
            test_question = "你好，请简单介绍一下自己"
            print(f"📝 测试问题: {test_question}")
            
            # 获取当前标签页句柄
            tab_handle = self.browser.driver.current_window_handle
            
            # 执行交互
            result = await model_handler.interact_with_model(
                self.yuanbao_config, 
                test_question, 
                tab_handle
            )
            
            # 显示结果
            print("\n📊 交互测试结果:")
            if result.get('success'):
                print("🎉 交互成功!")
                response = result.get('response', '')
                print(f"📄 腾讯元宝回复: {response}")
                print(f"📊 回复长度: {len(response)} 字符")
                
                # 保存结果
                with open("yuanbao_test_result.txt", "w", encoding="utf-8") as f:
                    f.write(f"腾讯元宝测试结果\n")
                    f.write(f"时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"问题: {test_question}\n")
                    f.write(f"回复: {response}\n")
                    f.write(f"使用的配置:\n")
                    for key, value in self.yuanbao_config.items():
                        f.write(f"  {key}: {value}\n")
                
                print("💾 结果已保存到: yuanbao_test_result.txt")
                return True
                
            else:
                print("❌ 交互失败!")
                print(f"🔍 错误信息: {result.get('error', '未知错误')}")
                return False
                
        except Exception as e:
            print(f"❌ 交互测试异常: {e}")
            self.logger.error(f"腾讯元宝交互测试异常: {e}")
            return False
    
    async def run_full_test(self):
        """运行完整测试"""
        print("🤖 腾讯元宝完整测试")
        print("=" * 60)
        
        try:
            # 1. 分析页面结构
            if not await self.analyze_yuanbao_page():
                return False
            
            # 2. 询问是否继续交互测试
            print("\n💡 页面分析完成！")
            choice = input("是否继续进行交互测试？(y/n): ").strip().lower()
            
            if choice == 'y':
                # 3. 测试交互功能
                interaction_success = await self.test_interaction()
            else:
                print("⏭️  跳过交互测试")
                interaction_success = None
            
            # 4. 保持浏览器打开
            print("\n💡 浏览器保持打开状态，您可以手动验证")
            input("按回车键关闭浏览器...")
            
            return interaction_success
            
        except Exception as e:
            print(f"\n❌ 测试过程中出现异常: {e}")
            self.logger.error(f"腾讯元宝测试异常: {e}")
            return False
        finally:
            # 清理资源
            if self.browser:
                self.browser.quit()

async def main():
    """主函数"""
    tester = YuanbaoTester()
    success = await tester.run_full_test()
    
    print("\n" + "=" * 60)
    if success is True:
        print("🎉 腾讯元宝测试完全成功!")
        print("✅ 所有选择器都能正常工作")
        print("✅ 可以正常发送消息和获取响应")
    elif success is False:
        print("⚠️  腾讯元宝测试需要调整配置")
        print("💡 请查看分析结果并更新选择器配置")
    else:
        print("📋 腾讯元宝页面分析完成")
        print("💡 请查看分析结果以确认选择器配置")

if __name__ == "__main__":
    asyncio.run(main())
