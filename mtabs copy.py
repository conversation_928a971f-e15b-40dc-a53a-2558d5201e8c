from browser_use import Agent
from langchain_openai import ChatOpenAI
import asyncio

async def main():
    agent = Agent(
        task="""
        1. 打开 Grok.com，用我的账号登录（记住密码）
        2. 输入提示词：“写一段关于新能源汽车的营销文案”
        3. 打开新标签页，访问 Claude.ai，上传文档《2025 新能源市场报告.pdf[](@replace=10001)》
        4. 提问：“提取报告中三大趋势，用 Markdown 表格展示”
        5. 再开新标签页，访问 Gemini，输入：“生成一张新能源汽车增长曲线图”
        6. 将所有结果保存到本地 report.docx
        """,
        llm=ChatOpenAI(model="gpt-4o"),  # 用 GPT-4o 调度任务
        use_vision=True  # 启用视觉识别
    )
    await agent.run()

asyncio.run(main())