"""
腾讯元宝发送按钮分析脚本
专门分析腾讯元宝页面的发送按钮，找到正确的选择器
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from core.browser_manager import BrowserManager
from utils.logger import app_logger

class YuanbaoSendButtonAnalyzer:
    def __init__(self):
        self.logger = app_logger
        self.browser = None
    
    def analyze_send_button(self):
        """分析腾讯元宝发送按钮"""
        print("🔍 腾讯元宝发送按钮分析")
        print("=" * 50)
        
        try:
            # 启动浏览器
            print("🚀 启动浏览器...")
            self.browser = BrowserManager(headless=False, user_data_dir=None)
            
            if not self.browser.start_browser():
                print("❌ 浏览器启动失败")
                return
            
            # 打开腾讯元宝网站
            print("🌐 打开腾讯元宝网站...")
            tab_handle = self.browser.open_tab("https://yuanbao.tencent.com", "腾讯元宝按钮分析")
            
            if not tab_handle:
                print("❌ 打开腾讯元宝网站失败")
                return
            
            # 等待页面加载
            time.sleep(5)
            
            print("✅ 页面加载完成")
            print("\n💡 请在浏览器中:")
            print("  1. 确保已登录腾讯元宝账号")
            print("  2. 在输入框中输入一些文字（不要发送）")
            print("  3. 然后按回车键继续分析发送按钮")
            
            input("完成上述步骤后，按回车键开始分析发送按钮...")
            
            # 分析发送按钮
            self.analyze_buttons()
            
            # 保持浏览器打开
            print("\n💡 浏览器保持打开状态，您可以继续手动测试")
            input("按回车键关闭浏览器...")
            
        except Exception as e:
            print(f"❌ 分析过程中出现异常: {e}")
            self.logger.error(f"按钮分析异常: {e}")
        finally:
            if self.browser:
                self.browser.quit()
    
    def analyze_buttons(self):
        """分析页面上的所有按钮"""
        print("\n🔍 开始分析页面按钮...")
        
        try:
            # 1. 查找所有按钮元素
            print("\n📋 查找所有按钮元素:")
            
            all_buttons = self.browser.driver.find_elements("css selector", "button")
            print(f"找到 {len(all_buttons)} 个按钮元素")
            
            send_button_candidates = []
            
            for i, button in enumerate(all_buttons):
                try:
                    # 获取按钮属性
                    text = button.text.strip()
                    aria_label = button.get_attribute("aria-label") or ""
                    title = button.get_attribute("title") or ""
                    class_name = button.get_attribute("class") or ""
                    data_testid = button.get_attribute("data-testid") or ""
                    type_attr = button.get_attribute("type") or ""
                    id_attr = button.get_attribute("id") or ""
                    
                    # 检查是否可能是发送按钮
                    is_send_candidate = any([
                        "发送" in text,
                        "发送" in aria_label,
                        "发送" in title,
                        "send" in class_name.lower(),
                        "submit" in class_name.lower(),
                        "send" in data_testid.lower(),
                        "send" in id_attr.lower(),
                        type_attr == "submit",
                        "发送" in class_name
                    ])
                    
                    # 显示所有按钮信息（前15个）或发送按钮候选者
                    if is_send_candidate or i < 15:
                        print(f"\n  按钮 [{i+1}]:")
                        print(f"    文本: '{text}'")
                        print(f"    aria-label: '{aria_label}'")
                        print(f"    title: '{title}'")
                        print(f"    class: '{class_name}'")
                        print(f"    data-testid: '{data_testid}'")
                        print(f"    id: '{id_attr}'")
                        print(f"    type: '{type_attr}'")
                        print(f"    是否可能是发送按钮: {'🎯 是' if is_send_candidate else '否'}")
                        
                        if is_send_candidate:
                            send_button_candidates.append({
                                'index': i,
                                'element': button,
                                'text': text,
                                'aria_label': aria_label,
                                'title': title,
                                'class': class_name,
                                'data_testid': data_testid,
                                'id': id_attr,
                                'type': type_attr
                            })
                
                except Exception as e:
                    print(f"    按钮 [{i+1}]: 获取属性失败 - {e}")
            
            # 2. 查找其他可能的发送元素
            print(f"\n🔍 查找其他可能的发送元素:")
            
            other_selectors = [
                "div[role='button']",
                "span[role='button']", 
                "[onclick*='send']",
                "[onclick*='发送']",
                ".send",
                ".submit",
                "[data-action*='send']"
            ]
            
            for selector in other_selectors:
                try:
                    elements = self.browser.driver.find_elements("css selector", selector)
                    if elements:
                        print(f"✅ {selector}: 找到 {len(elements)} 个元素")
                        for i, elem in enumerate(elements[:2]):
                            try:
                                text = elem.text.strip()
                                class_name = elem.get_attribute("class") or "无"
                                onclick = elem.get_attribute("onclick") or "无"
                                print(f"  [{i+1}] text: '{text}'")
                                print(f"      class: {class_name}")
                                print(f"      onclick: {onclick}")
                            except:
                                continue
                    else:
                        print(f"❌ {selector}: 未找到元素")
                except Exception as e:
                    print(f"❌ {selector}: 查询失败 - {e}")
            
            # 3. 分析发送按钮候选者
            print(f"\n🎯 发现 {len(send_button_candidates)} 个发送按钮候选者:")
            
            for i, candidate in enumerate(send_button_candidates):
                print(f"\n  候选者 {i+1}:")
                print(f"    索引: {candidate['index']}")
                print(f"    文本: '{candidate['text']}'")
                print(f"    aria-label: '{candidate['aria_label']}'")
                print(f"    class: '{candidate['class']}'")
                print(f"    data-testid: '{candidate['data_testid']}'")
                print(f"    id: '{candidate['id']}'")
                print(f"    type: '{candidate['type']}'")
            
            # 4. 生成建议的选择器
            print("\n💡 建议的发送按钮选择器:")
            
            suggested_selectors = []
            
            for candidate in send_button_candidates:
                # 基于不同属性生成选择器
                if candidate['data_testid']:
                    suggested_selectors.append(f"[data-testid='{candidate['data_testid']}']")
                
                if candidate['id']:
                    suggested_selectors.append(f"#{candidate['id']}")
                
                if candidate['aria_label']:
                    suggested_selectors.append(f"button[aria-label='{candidate['aria_label']}']")
                
                if candidate['type'] == 'submit':
                    suggested_selectors.append("button[type='submit']")
                
                # 基于class生成选择器
                if candidate['class']:
                    # 取class的第一个部分作为选择器
                    classes = candidate['class'].split()
                    if classes:
                        first_class = classes[0]
                        suggested_selectors.append(f"button.{first_class}")
                        
                        # 如果有多个class，也尝试组合
                        if len(classes) > 1:
                            suggested_selectors.append(f".{first_class}.{classes[1]}")
            
            # 去重并显示
            unique_selectors = list(dict.fromkeys(suggested_selectors))  # 保持顺序的去重
            
            print("推荐的submit_selector配置:")
            for i, selector in enumerate(unique_selectors[:5]):  # 只显示前5个
                print(f"  {i+1}. {selector}")
            
            if unique_selectors:
                # 生成组合选择器
                combined_selector = ", ".join(unique_selectors[:3])  # 取前3个组合
                print(f"\n组合选择器:")
                print(f'  "submit_selector": "{combined_selector}"')
            
            # 5. 测试点击功能
            if send_button_candidates:
                print(f"\n🧪 测试点击功能:")
                print("选择要测试的按钮:")
                for i, candidate in enumerate(send_button_candidates):
                    display_name = candidate['text'] or candidate['aria_label'] or candidate['id'] or f'按钮{candidate['index']}'
                    print(f"  {i+1}. {display_name}")
                
                try:
                    choice = input("输入数字选择要测试的按钮 (或按回车跳过): ").strip()
                    if choice and choice.isdigit():
                        choice_idx = int(choice) - 1
                        if 0 <= choice_idx < len(send_button_candidates):
                            test_button = send_button_candidates[choice_idx]['element']
                            display_name = send_button_candidates[choice_idx]['text'] or '无文本'
                            print(f"🧪 测试点击按钮: {display_name}")
                            
                            # 尝试点击
                            test_button.click()
                            print("✅ 点击成功!")
                            
                            # 等待一下看是否有反应
                            time.sleep(3)
                            print("💡 请检查腾讯元宝是否发送了消息")
                        else:
                            print("❌ 无效选择")
                except Exception as e:
                    print(f"❌ 点击测试失败: {e}")
            
            # 6. 保存分析结果
            try:
                with open("yuanbao_send_button_analysis.txt", "w", encoding="utf-8") as f:
                    f.write("腾讯元宝发送按钮分析结果\n")
                    f.write(f"分析时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write("=" * 50 + "\n\n")
                    
                    f.write(f"总按钮数: {len(all_buttons)}\n")
                    f.write(f"发送按钮候选者数: {len(send_button_candidates)}\n\n")
                    
                    f.write("发送按钮候选者详情:\n")
                    for i, candidate in enumerate(send_button_candidates):
                        f.write(f"\n候选者 {i+1}:\n")
                        f.write(f"  文本: {candidate['text']}\n")
                        f.write(f"  aria-label: {candidate['aria_label']}\n")
                        f.write(f"  title: {candidate['title']}\n")
                        f.write(f"  class: {candidate['class']}\n")
                        f.write(f"  data-testid: {candidate['data_testid']}\n")
                        f.write(f"  id: {candidate['id']}\n")
                        f.write(f"  type: {candidate['type']}\n")
                    
                    f.write("\n建议的选择器:\n")
                    for selector in unique_selectors:
                        f.write(f"  {selector}\n")
                
                print("📄 详细分析结果已保存到: yuanbao_send_button_analysis.txt")
                
            except Exception as e:
                print(f"❌ 保存分析结果失败: {e}")
                
        except Exception as e:
            print(f"❌ 按钮分析失败: {e}")

def main():
    """主函数"""
    analyzer = YuanbaoSendButtonAnalyzer()
    analyzer.analyze_send_button()

if __name__ == "__main__":
    main()
