"""
多标签页AI模型查询系统
主程序入口
"""

import asyncio
import click
import sys
from pathlib import Path
from typing import List, Optional

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from core.task_scheduler import TaskScheduler
from utils.logger import app_logger

class MultiTabAIQuery:
    def __init__(self):
        self.scheduler = TaskScheduler()
        self.logger = app_logger
    
    async def run_interactive(self):
        """运行交互式模式"""
        print("🤖 多标签页AI模型查询系统")
        print("=" * 50)
        
        # 显示可用模型
        available_models = self.scheduler.get_available_models()
        if not available_models:
            print("❌ 没有配置任何AI模型，请检查配置文件")
            return
        
        print("📋 可用的AI模型:")
        for i, model in enumerate(available_models, 1):
            print(f"  {i}. {model}")
        
        # 选择模型
        print("\n🔧 请选择要使用的模型 (输入数字，用逗号分隔，或按回车使用全部):")
        selection = input("选择: ").strip()
        
        selected_models = None
        if selection:
            try:
                indices = [int(x.strip()) - 1 for x in selection.split(',')]
                selected_models = [available_models[i] for i in indices if 0 <= i < len(available_models)]
                print(f"✅ 已选择模型: {', '.join(selected_models)}")
            except (ValueError, IndexError):
                print("⚠️  选择格式错误，将使用所有模型")
        else:
            print("✅ 将使用所有模型")
        
        # 输入问题
        print("\n❓ 请输入要查询的问题:")
        question = input("问题: ").strip()
        
        if not question:
            print("❌ 问题不能为空")
            return
        
        # 执行查询
        print(f"\n🚀 开始查询: {question}")
        print("⏳ 正在处理，请稍候...")
        
        try:
            result = await self.scheduler.run_query(question, selected_models)
            
            if result.get('success'):
                print("\n✅ 查询完成!")
                print(f"📊 成功响应: {result['analysis']['successful_models']}/{result['analysis']['total_models']}")
                print(f"📁 报告文件: {list(result['output_files'].values())}")
                
                # 显示简要结果
                if result['summary']['consolidated_insights']:
                    print("\n💡 关键洞察:")
                    for insight in result['summary']['consolidated_insights'][:3]:  # 只显示前3个
                        print(f"  • {insight[:100]}...")
            else:
                print(f"\n❌ 查询失败: {result.get('error', '未知错误')}")
                
        except Exception as e:
            print(f"\n❌ 执行过程中出错: {e}")
            self.logger.error(f"交互式查询失败: {e}")

@click.group()
def cli():
    """多标签页AI模型查询系统"""
    pass

@cli.command()
@click.option('--question', '-q', required=True, help='要查询的问题')
@click.option('--models', '-m', help='指定模型名称，用逗号分隔')
@click.option('--output', '-o', default='markdown', help='输出格式 (markdown/json/html)')
def query(question: str, models: Optional[str], output: str):
    """执行AI模型查询"""
    async def run_query():
        scheduler = TaskScheduler()
        
        selected_models = None
        if models:
            selected_models = [m.strip() for m in models.split(',')]
        
        # 更新输出格式设置
        scheduler.update_settings({'output_format': output})
        
        result = await scheduler.run_query(question, selected_models)
        
        if result.get('success'):
            click.echo(f"✅ 查询完成! 报告文件: {list(result['output_files'].values())}")
        else:
            click.echo(f"❌ 查询失败: {result.get('error')}")
    
    asyncio.run(run_query())

@cli.command()
def models():
    """显示可用的AI模型"""
    scheduler = TaskScheduler()
    available_models = scheduler.get_available_models()
    
    if available_models:
        click.echo("可用的AI模型:")
        for model in available_models:
            click.echo(f"  • {model}")
    else:
        click.echo("没有配置任何AI模型")

@cli.command()
def interactive():
    """运行交互式模式"""
    async def run_interactive():
        app = MultiTabAIQuery()
        await app.run_interactive()
    
    asyncio.run(run_interactive())

async def main():
    """主函数 - 默认运行交互式模式"""
    app = MultiTabAIQuery()
    await app.run_interactive()

if __name__ == "__main__":
    # 如果有命令行参数，使用CLI模式
    if len(sys.argv) > 1:
        cli()
    else:
        # 否则运行交互式模式
        asyncio.run(main())
