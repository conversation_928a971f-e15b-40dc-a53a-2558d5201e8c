"""
浏览器管理模块
负责管理浏览器实例和标签页操作
"""

import asyncio
import time
from typing import List, Dict, Optional
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from utils.logger import app_logger
from tools.utils import get_default_browser_path

class BrowserManager:
    def __init__(self, headless=False, user_data_dir=None):
        """
        初始化浏览器管理器
        
        Args:
            headless: 是否无头模式
            user_data_dir: 用户数据目录，用于复用cookie
        """
        self.driver = None
        self.tabs = {}  # 存储标签页句柄和对应的模型信息
        self.headless = headless
        self.user_data_dir = user_data_dir
        self.logger = app_logger
        
    def start_browser(self):
        """启动浏览器"""
        try:
            chrome_options = Options()
            
            # 设置用户数据目录以复用cookie
            if self.user_data_dir:
                chrome_options.add_argument(f"--user-data-dir={self.user_data_dir}")
            
            # 其他有用的选项
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            if self.headless:
                chrome_options.add_argument("--headless")
            
            # 尝试使用默认浏览器路径
            try:
                browser_path = get_default_browser_path()
                chrome_options.binary_location = browser_path
                self.logger.info(f"使用浏览器路径: {browser_path}")
            except Exception as e:
                self.logger.warning(f"无法获取默认浏览器路径，使用系统默认: {e}")
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            self.logger.info("浏览器启动成功")
            return True
            
        except Exception as e:
            self.logger.error(f"浏览器启动失败: {e}")
            return False
    
    def open_tab(self, url: str, model_name: str) -> Optional[str]:
        """
        打开新标签页
        
        Args:
            url: 要打开的URL
            model_name: 模型名称
            
        Returns:
            标签页句柄
        """
        try:
            # 打开新标签页
            self.driver.execute_script("window.open('');")
            
            # 切换到新标签页
            new_tab = self.driver.window_handles[-1]
            self.driver.switch_to.window(new_tab)
            
            # 访问URL
            self.driver.get(url)
            
            # 存储标签页信息
            self.tabs[new_tab] = {
                'model_name': model_name,
                'url': url,
                'status': 'opened'
            }
            
            self.logger.info(f"成功打开标签页: {model_name} - {url}")
            return new_tab
            
        except Exception as e:
            self.logger.error(f"打开标签页失败: {model_name} - {e}")
            return None
    
    def switch_to_tab(self, tab_handle: str) -> bool:
        """切换到指定标签页"""
        try:
            self.driver.switch_to.window(tab_handle)
            return True
        except Exception as e:
            self.logger.error(f"切换标签页失败: {e}")
            return False
    
    def wait_for_element(self, selector: str, timeout: int = 10, by: By = By.CSS_SELECTOR):
        """等待元素出现"""
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((by, selector))
            )
            return element
        except TimeoutException:
            self.logger.warning(f"等待元素超时: {selector}")
            return None
    
    def send_input(self, selector: str, text: str, timeout: int = 10) -> bool:
        """向输入框发送文本"""
        try:
            element = self.wait_for_element(selector, timeout)
            if element:
                element.clear()
                element.send_keys(text)
                self.logger.info(f"成功发送输入: {text[:50]}...")
                return True
            return False
        except Exception as e:
            self.logger.error(f"发送输入失败: {e}")
            return False
    
    def click_element(self, selector: str, timeout: int = 10) -> bool:
        """点击元素"""
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
            )
            element.click()
            self.logger.info(f"成功点击元素: {selector}")
            return True
        except Exception as e:
            self.logger.error(f"点击元素失败: {e}")
            return False
    
    def get_text(self, selector: str, timeout: int = 10) -> Optional[str]:
        """获取元素文本"""
        try:
            element = self.wait_for_element(selector, timeout)
            if element:
                return element.text
            return None
        except Exception as e:
            self.logger.error(f"获取文本失败: {e}")
            return None
    
    def close_tab(self, tab_handle: str):
        """关闭指定标签页"""
        try:
            self.driver.switch_to.window(tab_handle)
            self.driver.close()
            if tab_handle in self.tabs:
                del self.tabs[tab_handle]
            self.logger.info(f"成功关闭标签页: {tab_handle}")
        except Exception as e:
            self.logger.error(f"关闭标签页失败: {e}")
    
    def quit(self):
        """退出浏览器"""
        try:
            if self.driver:
                self.driver.quit()
                self.logger.info("浏览器已退出")
        except Exception as e:
            self.logger.error(f"退出浏览器失败: {e}")
    
    def get_current_tab_info(self) -> Dict:
        """获取当前标签页信息"""
        current_handle = self.driver.current_window_handle
        return self.tabs.get(current_handle, {})
