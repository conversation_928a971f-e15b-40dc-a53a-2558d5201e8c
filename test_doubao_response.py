"""
豆包响应测试脚本
专门测试豆包AI的响应获取功能
"""

import asyncio
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from core.browser_manager import BrowserManager
from utils.logger import app_logger

class DoubaoResponseTester:
    def __init__(self):
        self.logger = app_logger
        self.browser = None
        
        # 更新后的豆包配置
        self.doubao_config = {
            "name": "豆包",
            "url": "https://www.doubao.com",
            "login_required": True,
            "input_selector": "textarea.semi-input-textarea",
            "submit_selector": "button[aria-label*='发送'], button[title*='发送'], button:has(svg)",
            "output_selector": "[data-testid='message_text_content'], [data-testid='receive_message'], .container-jJBCG1, [data-testid='message-block-container']",
            "wait_time": 8,
            "description": "字节跳动豆包AI"
        }
    
    async def test_full_interaction(self):
        """测试完整的豆包交互流程"""
        print("🤖 豆包响应获取测试")
        print("=" * 50)
        
        try:
            # 1. 启动浏览器
            print("🚀 启动浏览器...")
            self.browser = BrowserManager(headless=False, user_data_dir=None)
            
            if not self.browser.start_browser():
                print("❌ 浏览器启动失败")
                return False
            
            print("✅ 浏览器启动成功")
            
            # 2. 打开豆包网站
            print("\n🌐 打开豆包网站...")
            tab_handle = self.browser.open_tab(self.doubao_config['url'], self.doubao_config['name'])
            
            if not tab_handle:
                print("❌ 打开豆包网站失败")
                return False
            
            print("✅ 成功打开豆包网站")
            
            # 等待页面加载
            await asyncio.sleep(5)
            
            # 3. 检查登录状态
            print("\n🔐 检查登录状态...")
            print("💡 请确保您已经登录豆包账号")
            print("📍 如果看到登录页面，请先手动登录")
            
            # 等待用户确认登录
            input("确认已登录后，按回车键继续...")
            
            # 4. 查找输入框
            print("\n🔍 查找输入框...")
            input_element = self.browser.wait_for_element(self.doubao_config['input_selector'], timeout=10)
            
            if not input_element:
                print("❌ 未找到输入框")
                return False
            
            print("✅ 找到输入框")
            
            # 5. 发送测试消息
            test_message = "你好，请简单介绍一下自己"
            print(f"\n📝 发送测试消息: {test_message}")
            
            # 清空输入框并输入消息
            input_element.clear()
            input_element.send_keys(test_message)
            
            print("✅ 消息已输入")
            
            # 6. 查找并点击发送按钮
            print("\n🔍 查找发送按钮...")
            
            # 尝试多种发送方式
            send_success = False
            
            # 方式1: 尝试点击发送按钮
            try:
                send_button = self.browser.wait_for_element(self.doubao_config['submit_selector'], timeout=5)
                if send_button:
                    send_button.click()
                    print("✅ 通过点击按钮发送消息")
                    send_success = True
            except:
                pass
            
            # 方式2: 如果按钮点击失败，尝试按回车键
            if not send_success:
                try:
                    from selenium.webdriver.common.keys import Keys
                    input_element.send_keys(Keys.RETURN)
                    print("✅ 通过回车键发送消息")
                    send_success = True
                except:
                    pass
            
            if not send_success:
                print("❌ 发送消息失败")
                return False
            
            # 7. 等待响应
            print("\n⏳ 等待豆包响应...")
            print("💡 请耐心等待，豆包正在生成回答...")
            
            # 等待响应生成
            await asyncio.sleep(10)
            
            # 8. 尝试获取响应
            print("\n🔍 尝试获取响应...")
            
            response_selectors = [
                "[data-testid='message_text_content']",
                "[data-testid='receive_message']", 
                ".container-jJBCG1",
                "[data-testid='message-block-container']",
                "[data-testid='message_content']"
            ]
            
            response_found = False
            response_text = ""
            
            for selector in response_selectors:
                try:
                    print(f"🔍 尝试选择器: {selector}")
                    elements = self.browser.driver.find_elements("css selector", selector)
                    
                    if elements:
                        print(f"✅ 找到 {len(elements)} 个元素")
                        
                        # 获取最后一个元素的文本（通常是最新的响应）
                        for i, element in enumerate(elements[-3:]):  # 检查最后3个元素
                            try:
                                text = element.text.strip()
                                if text and len(text) > 20:  # 确保有实际内容
                                    print(f"📄 元素[{i}]内容预览: {text[:100]}...")
                                    if not response_found or len(text) > len(response_text):
                                        response_text = text
                                        response_found = True
                            except:
                                continue
                    else:
                        print(f"❌ 选择器未找到元素: {selector}")
                        
                except Exception as e:
                    print(f"❌ 选择器测试失败: {selector} - {e}")
            
            # 9. 显示结果
            print("\n" + "=" * 50)
            print("📊 测试结果:")
            
            if response_found:
                print("🎉 成功获取到豆包的响应!")
                print(f"📄 响应长度: {len(response_text)} 字符")
                print(f"📝 响应内容预览:")
                print("-" * 30)
                print(response_text[:500] + ("..." if len(response_text) > 500 else ""))
                print("-" * 30)
                
                # 保存完整响应到文件
                with open("doubao_response_test.txt", "w", encoding="utf-8") as f:
                    f.write(f"豆包响应测试结果\n")
                    f.write(f"时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"问题: {test_message}\n")
                    f.write(f"响应长度: {len(response_text)} 字符\n")
                    f.write(f"完整响应:\n{response_text}")
                
                print("💾 完整响应已保存到: doubao_response_test.txt")
                
            else:
                print("❌ 未能获取到豆包的响应")
                print("💡 可能的原因:")
                print("  1. 豆包还在生成回答，需要等待更长时间")
                print("  2. 页面结构发生了变化")
                print("  3. 网络连接问题")
                print("  4. 需要重新登录")
            
            # 保持浏览器打开以便手动检查
            print("\n💡 浏览器将保持打开状态，您可以手动检查")
            print("📍 请查看豆包网站中是否有响应显示")
            input("按回车键关闭浏览器...")
            
            return response_found
            
        except Exception as e:
            print(f"\n❌ 测试过程中出现异常: {e}")
            self.logger.error(f"豆包响应测试异常: {e}")
            return False
        finally:
            # 清理资源
            if self.browser:
                self.browser.quit()

async def main():
    """主函数"""
    tester = DoubaoResponseTester()
    success = await tester.test_full_interaction()
    
    if success:
        print("\n🎉 豆包响应测试成功!")
        print("✅ 新的选择器配置可以正确获取响应")
    else:
        print("\n⚠️  豆包响应测试需要进一步调试")
        print("💡 建议检查:")
        print("  1. 确保已正确登录豆包")
        print("  2. 检查网络连接")
        print("  3. 等待更长时间让豆包生成回答")

if __name__ == "__main__":
    asyncio.run(main())
