"""
AI模型处理模块
负责与不同AI模型网站的交互
"""

import time
import asyncio
from typing import Dict, List, Optional
from core.browser_manager import BrowserManager
from utils.logger import app_logger

class ModelHandler:
    def __init__(self, browser_manager: BrowserManager):
        """
        初始化模型处理器
        
        Args:
            browser_manager: 浏览器管理器实例
        """
        self.browser = browser_manager
        self.logger = app_logger
        
    async def interact_with_model(self, model_config: Dict, question: str, tab_handle: str) -> Dict:
        """
        与AI模型交互
        
        Args:
            model_config: 模型配置信息
            question: 要问的问题
            tab_handle: 标签页句柄
            
        Returns:
            包含响应结果的字典
        """
        result = {
            'model_name': model_config['name'],
            'question': question,
            'response': None,
            'success': False,
            'error': None,
            'timestamp': time.time()
        }
        
        try:
            # 切换到对应标签页
            if not self.browser.switch_to_tab(tab_handle):
                result['error'] = "无法切换到标签页"
                return result
            
            self.logger.info(f"开始与 {model_config['name']} 交互")
            
            # 等待页面加载
            await asyncio.sleep(2)
            
            # 检查是否需要登录
            if model_config.get('login_required', False):
                login_success = await self._handle_login(model_config)
                if not login_success:
                    result['error'] = "登录失败"
                    return result
            
            # 发送问题
            success = await self._send_question(model_config, question)
            if not success:
                result['error'] = "发送问题失败"
                return result
            
            # 等待响应
            response = await self._wait_for_response(model_config)
            if response:
                result['response'] = response
                result['success'] = True
                self.logger.info(f"{model_config['name']} 响应成功")
            else:
                result['error'] = "获取响应失败"
                
        except Exception as e:
            result['error'] = str(e)
            self.logger.error(f"{model_config['name']} 交互失败: {e}")
        
        return result
    
    async def _handle_login(self, model_config: Dict) -> bool:
        """
        处理登录逻辑
        
        Args:
            model_config: 模型配置
            
        Returns:
            登录是否成功
        """
        try:
            # 检查是否已经登录
            # 这里可以根据不同网站的特征来判断登录状态
            # 例如检查是否存在用户头像、用户名等元素
            
            # 简单的登录状态检查
            login_indicators = [
                "avatar", "user-info", "profile", "logout", "用户", "头像"
            ]
            
            for indicator in login_indicators:
                elements = self.browser.driver.find_elements("css selector", f"[class*='{indicator}']")
                if elements:
                    self.logger.info(f"{model_config['name']} 已登录")
                    return True
            
            # 如果没有检测到登录状态，等待用户手动登录
            self.logger.warning(f"{model_config['name']} 需要手动登录，请在浏览器中完成登录")
            
            # 等待一段时间让用户登录
            await asyncio.sleep(10)
            
            # 再次检查登录状态
            for indicator in login_indicators:
                elements = self.browser.driver.find_elements("css selector", f"[class*='{indicator}']")
                if elements:
                    self.logger.info(f"{model_config['name']} 登录成功")
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"登录处理失败: {e}")
            return False
    
    async def _send_question(self, model_config: Dict, question: str) -> bool:
        """
        发送问题到AI模型
        
        Args:
            model_config: 模型配置
            question: 问题文本
            
        Returns:
            发送是否成功
        """
        try:
            input_selector = model_config.get('input_selector', 'textarea')
            submit_selector = model_config.get('submit_selector', 'button[type="submit"]')
            
            # 等待输入框出现
            await asyncio.sleep(2)
            
            # 发送文本到输入框
            if not self.browser.send_input(input_selector, question):
                return False
            
            # 等待一下再点击发送
            await asyncio.sleep(1)
            
            # 点击发送按钮
            if not self.browser.click_element(submit_selector):
                # 尝试按回车键
                try:
                    element = self.browser.wait_for_element(input_selector)
                    if element:
                        element.send_keys("\n")
                        return True
                except:
                    pass
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"发送问题失败: {e}")
            return False
    
    async def _wait_for_response(self, model_config: Dict) -> Optional[str]:
        """
        等待AI模型响应
        
        Args:
            model_config: 模型配置
            
        Returns:
            响应文本
        """
        try:
            output_selector = model_config.get('output_selector', '.message')
            wait_time = model_config.get('wait_time', 10)
            max_wait = 60  # 最大等待时间
            
            self.logger.info(f"等待 {model_config['name']} 响应...")
            
            # 等待响应生成
            await asyncio.sleep(wait_time)
            
            # 尝试获取响应
            start_time = time.time()
            while time.time() - start_time < max_wait:
                try:
                    # 获取所有响应元素
                    elements = self.browser.driver.find_elements("css selector", output_selector)
                    if elements:
                        # 获取最后一个响应（通常是最新的）
                        latest_response = elements[-1].text.strip()
                        if latest_response and len(latest_response) > 10:  # 确保有实际内容
                            return latest_response
                    
                    await asyncio.sleep(2)
                    
                except Exception as e:
                    self.logger.debug(f"获取响应时出错: {e}")
                    await asyncio.sleep(2)
            
            # 如果超时，尝试获取页面上的任何文本内容
            try:
                page_text = self.browser.driver.find_element("css selector", "body").text
                if page_text:
                    return f"超时获取的页面内容: {page_text[:1000]}..."
            except:
                pass
            
            return None
            
        except Exception as e:
            self.logger.error(f"等待响应失败: {e}")
            return None
    
    async def batch_interact(self, models_config: List[Dict], question: str, tab_handles: Dict[str, str]) -> List[Dict]:
        """
        批量与多个AI模型交互
        
        Args:
            models_config: 模型配置列表
            question: 问题
            tab_handles: 模型名称到标签页句柄的映射
            
        Returns:
            所有模型的响应结果列表
        """
        tasks = []
        
        for model_config in models_config:
            model_name = model_config['name']
            if model_name in tab_handles:
                task = self.interact_with_model(
                    model_config, 
                    question, 
                    tab_handles[model_name]
                )
                tasks.append(task)
        
        # 并发执行所有任务
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    'model_name': models_config[i]['name'],
                    'question': question,
                    'response': None,
                    'success': False,
                    'error': str(result),
                    'timestamp': time.time()
                })
            else:
                processed_results.append(result)
        
        return processed_results
